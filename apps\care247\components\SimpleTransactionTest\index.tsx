import React, { useState } from 'react';
import { Card, Button, Input, Space, Typography, Alert } from 'antd';
import { useTransactionStatus } from '../../hooks/useTransactionStatus';

const { Title, Text } = Typography;

const SimpleTransactionTest: React.FC = () => {
  const [transactionId, setTransactionId] = useState('TEST_SUCCESS_123');
  const [enabled, setEnabled] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // Simple mock params
  const mockParams = {
    transactionId,
    expiredTime: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
    redirectUrl: '/',
    partnerId: 'care247'
  };

  const {
    transactionStatus,
    isCheckingStatus,
    statusError,
    isSuccess,
    checkCount
  } = useTransactionStatus({
    transactionId,
    params: mockParams,
    enabled,
    onSuccess: (status) => {
      console.log('SimpleTransactionTest - onSuccess called with:', status);
      setSuccessMessage(`Success! Status: ${status.status}, Partner: ${status.partnerId}`);
    },
    onError: (error) => {
      console.log('SimpleTransactionTest - onError called with:', error);
      setErrorMessage(error);
    }
  });

  const handleStart = () => {
    console.log('SimpleTransactionTest - Starting test with:', { transactionId, mockParams });
    setSuccessMessage('');
    setErrorMessage('');
    setEnabled(true);
  };

  const handleStop = () => {
    console.log('SimpleTransactionTest - Stopping test');
    setEnabled(false);
  };

  const handleReset = () => {
    console.log('SimpleTransactionTest - Resetting test');
    setEnabled(false);
    setSuccessMessage('');
    setErrorMessage('');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '600px', margin: '0 auto' }}>
      <Title level={2}>Simple Transaction Test</Title>
      <Text type="secondary">
        Test đơn giản để debug lỗi transaction status
      </Text>

      <Card title="Test Controls" style={{ marginTop: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Transaction ID:</Text>
            <Input
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Nhập transaction ID"
              style={{ marginTop: '8px' }}
              disabled={enabled}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Tip: Sử dụng "TEST_SUCCESS_123" để test success case
            </Text>
          </div>
          
          <Space>
            <Button 
              type="primary" 
              onClick={handleStart}
              disabled={enabled}
              loading={isCheckingStatus}
            >
              Start Test
            </Button>
            <Button 
              onClick={handleStop}
              disabled={!enabled}
            >
              Stop Test
            </Button>
            <Button onClick={handleReset}>
              Reset
            </Button>
          </Space>
        </Space>
      </Card>

      <Card title="Test Results" style={{ marginTop: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Enabled: </Text>
            <Text>{enabled ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Is Checking: </Text>
            <Text>{isCheckingStatus ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Check Count: </Text>
            <Text>{checkCount}</Text>
          </div>
          <div>
            <Text strong>Is Success: </Text>
            <Text>{isSuccess ? 'Yes' : 'No'}</Text>
          </div>
          
          {successMessage && (
            <Alert
              message="Success Callback Triggered"
              description={successMessage}
              type="success"
              showIcon
            />
          )}
          
          {errorMessage && (
            <Alert
              message="Error Callback Triggered"
              description={errorMessage}
              type="error"
              showIcon
            />
          )}
          
          {statusError && (
            <Alert
              message="Status Error"
              description={statusError}
              type="warning"
              showIcon
            />
          )}
          
          {transactionStatus && (
            <div>
              <Text strong>Transaction Status:</Text>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '8px', 
                borderRadius: '4px',
                fontSize: '12px',
                marginTop: '8px'
              }}>
                {JSON.stringify(transactionStatus, null, 2)}
              </pre>
            </div>
          )}
        </Space>
      </Card>

      <Card title="Console Logs" style={{ marginTop: '16px' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Mở Developer Tools → Console để xem logs chi tiết:
          <br />
          • SimpleTransactionTest - Starting test with: ...
          <br />
          • useTransactionStatus - Transaction status received: ...
          <br />
          • checkTransactionStatusSaga - Starting with action: ...
          <br />
          • SimpleTransactionTest - onSuccess called with: ...
        </Text>
      </Card>
    </div>
  );
};

export default SimpleTransactionTest;
