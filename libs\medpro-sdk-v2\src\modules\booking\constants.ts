export const RESERVE_BOOKING = 'booking-gateway/reserve'
export const RESERVE_MULTI_BOOKINGS = 'booking-gateway/reserve-multiple'
export const COMPLAIN_BOOKING = 'booking-gateway/complain-booking'
export const CANCEL_RESERVATION_BOOKING = 'booking-gateway/cancel-reservation'
export const GET_ALL_BOOKING_2 = 'booking-gateway/all-bookings'
export const GET_ALL_INVISIBLE_BOOKING =
  'booking-gateway/all-invisible-bookings'
export const GET_ALL_BOOKING_BY_USER_ID = 'booking-gateway/all-bookings-by-user'
export const GET_ALL_BOOKING_BY = 'booking-gateway/all-bookings'
export const GET_ALL_INVISIBLE_BOOKING_BY_USER_ID =
  'booking-gateway/all-invisible-bookings-by-user'
export const INVISIBLE_BOOKING = 'booking-gateway/invisible'
export const GET_BOOKING_WITH_TRANSACTION_CODE =
  'booking-gateway/get-booking-by-transaction-code'
export const CHECK_TRANSACTION = 'booking-gateway/check-transaction'
export const RE_PAYMENT = 'booking-gateway/re-payment'
export const BOOKING_GATEWAY_PAYMENT_FEE = 'booking-gateway/payment-fee'
export const UPDATE_BOOKING_TELEMED_STATUS =
  'booking-gateway/update-booking-telemed-status'
export const PAYMENT_FEE_TRACKING = 'booking-gateway/payment-fee-tracking'
export const CHORAY_EXAM_RESULT = 'booking-gateway/exam-results'
export const CHORAY_GET_HEALTH_HISTORIES =
  'booking-gateway/get-health-histories'
export const CHORAY_GET_HEALTH_EXAM_HISTORIES =
  'booking-gateway/get-health-exam-histories'
export const CHORAY_GET_DETAIL_HEALTH_HISTORY =
  'booking-gateway/get-detail-health-history'
export const USER_ACCOUNT_CREATE_DEPOSIT = 'booking-gateway/create-deposit'
export const BOOKING_SHARE_TO_PAY = 'booking-gateway/booking-share-to-pay'
export const RE_PAYMENT_SHARE_TO_PAY = 'booking-gateway/re-payment-share-to-pay'
export const HISTORY_EXAM_BY_USER = 'booking-gateway/historyExamByUser'
export const HISTORY_EXAM_BY_PATIENT = 'booking-gateway/historyExamByPatient'
export const HISTORY_EXAM = 'booking-gateway/historyExam'
export const HISTORY_EXAM_GROUP = 'booking-gateway/historyExamGroup'
export const HISTORY_EXAM_GROUP_DETAIL =
  'booking-gateway/historyExamGroupDetail'
export const GET_BOOKINGS_AND_RESULTS =
  'booking-gateway/get-bookings-and-results'
export const GET_BOOKING_WITH_FILES = 'booking-gateway/get-file'
export const UPDATE_BOOKING_TO_SHARE_PAYMENT =
  'booking-gateway/change-booking-status'
export const GET_PAYMENT_FEE_HISTORY = 'booking-gateway/payment-fee-history'
export const GET_PAYMENT = 'booking-gateway/get-payment'
export const GET_PAYMENT_STATUS = 'booking-gateway/get-payment-status'
export const DECODE_SHORT_LINK = 'booking-gateway/decode-short-link'
export const UPDATE_BOOKING = 'booking-gateway/update-booking'
export const GET_BOOKING_UPDATE_HISTORY =
  'booking-gateway/booking-update-history'
export const GET_ALL_CANCELLATION_REASON =
  'booking-gateway/cancellation-reasons'
export const RETRY_SYNC_BOOKING_V1 = 'booking-gateway/retry-sync-booking'
export const VALID_RE_EXAM_DATE = 'mongo/service/valid-re-exam-date'
export const TRACKING_MEDPRO_CARE = 'booking-gateway/medpro-care/tracking'
export const RESERVE_MEDPRO_CARE = 'booking-gateway/medpro-care/reserve'
export const MEDPRO_CARE_ADD_ON = 'booking-gateway/medpro-care/addon'
export const MEDPRO_CARE_ADD_ON_CSKH = 'booking-gateway/medpro-care/addon-cskh'
export const RESERVE_MEDPRO_CARE_ADDON =
  'booking-gateway/medpro-care/addon/reserve'
export const RESERVE_MEDPRO_CARE_CSKH =
  'booking-gateway/medpro-care/reserve-cskh'
export const RESERVE_MEDPRO_CARE_ADDON_CSKH =
  'booking-gateway/medpro-care/addon/reserve-cskh'
export const RE_PAYMENT_NO_AUTH = 'booking-gateway/reserve-by-transation'
export const SHARE_BOOKING = 'booking-gateway/share-booking'
export const SHARE_BOOKING_INFO = 'booking-gateway/get-share-booking'
// Cancel Booking
export const CANCELLEATION_REASON = 'booking/cancellation-reasons'
export const CANCEL_BOOKING_V2 = 'booking/cancel'
export const GET_BANK_LIST = 'payment-method/get-bank-list'
export const GET_CARE247_INDEPENDENT =
  'cskh/care247-independent'
