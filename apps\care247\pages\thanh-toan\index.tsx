import React, { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/router'
import { Row, Col, Spin, Alert, message, Modal, Button } from 'antd'
import { CheckCircleOutlined } from '@ant-design/icons'
import styles from './styles.module.less'
import LeftPanel from '../../components/LeftPanel'
import PaymentCard from '../../components/PaymentCard'
import usePayment from '../../hooks/usePayment'
import { useTransactionStatus } from '../../hooks/useTransactionStatus'

// Fallback mock data
// const mockData = {
//   orderCode: 'T250M1204CA',
//   customerName: 'Vũ Thị <PERSON>',
//   amount: 122000,
//   service: 'Trẻ sơ sinh, ghế nôi',
//   bankAccount: '9646474000000891799',
//   bankName: 'MASTERBANK - Ngân hàng TMCP Việt Nam',
//   transferCode: '*****************'
// }

export function ThanhToanPage() {
  const router = useRouter()
  const { transactionId, partnerId, language = 'vi' } = router.query

  // State for UI components
  const [doMore, setDoMore] = useState(true)
  const [openSuccessModal, setOpenSuccessModal] = useState({ isOpen: false, redirectUrl: '' })

  const {
    paymentInfo,
    loading,
    translateData,
    translateLoading,
    getPaymentInfo,
    getTranslate,
    resetState
  } = usePayment()

  // Helper function to get translated text
  const getTranslatedText = React.useCallback(
    (key: string, defaultText = '') => {
      return translateData?.[key] || defaultText
    },
    [translateData]
  )

  // Prepare payment params for transaction status checking
  const paymentParams = useMemo(() => {
    if (!paymentInfo) return null

    return {
      transactionId: transactionId as string,
      expiredTime: router.query?.expiredTime as string,
      redirectUrl: router.query?.redirectUrl as string,
      qrCodeUrl: router.query?.qrCodeUrl as string,
      bankCode: router.query?.bankCode as string,
      bankName: router.query?.bankName as string,
      accountNo: router.query?.accountNo as string,
      accountName: router.query?.accountName as string,
      description: router.query?.description as string,
      ...paymentInfo
    }
  }, [paymentInfo, transactionId, router.query])

  // Use transaction status hook
  const {
    transactionStatus,
    isCheckingStatus,
    isSuccess,
    stopChecking
  } = useTransactionStatus({
    transactionId: transactionId as string,
    params: paymentParams,
    enabled: !!(transactionId && paymentParams && doMore),
    onSuccess: (status) => {
      console.log('Payment successful:', status)
      if (status.partnerId === "care247") {
        setOpenSuccessModal({
          isOpen: true,
          redirectUrl: router.query?.redirectUrl as string || '/'
        })
      }
    },
    onError: (error) => {
      console.error('Transaction status error:', error)
      message.error('Có lỗi xảy ra khi kiểm tra trạng thái thanh toán')
    }
  })

  // Fetch translate data using saga
  useEffect(() => {
    getTranslate({ language: language as string })
  }, [language, getTranslate])

  // Fetch payment info when component mounts
  useEffect(() => {
    if (transactionId && partnerId) {
      getPaymentInfo({
        transactionId: transactionId as string,
        partnerId: partnerId as string
      })
    }
  }, [transactionId, partnerId, getPaymentInfo])

  // Handle countdown expiration
  const handleCountdownExpired = () => {
    setDoMore(false)
    stopChecking()
    message.warning('Thời gian thanh toán đã hết!')
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      resetState()
      stopChecking()
    }
  }, [resetState, stopChecking])

  // Use API data if available, otherwise fallback to mock data
  const paymentData = useMemo(() => {
    return paymentInfo
      ? {
          orderCode: paymentInfo.feeCode,
          customerName: paymentInfo.hospitalName,
          amount: paymentInfo.amount,
          service: getTranslatedText(
            'medical_fee',
            `Phí khám bệnh - ${paymentInfo.hospitalName}`
          ),
          bankCode: router.query?.bankCode,
          accountName: router.query?.accountName,
          accountNo: router.query?.accountNo,
          transferCode: paymentInfo.feeCode,
          subTotal: paymentInfo.subTotal,
          totalFee: paymentInfo.totalFee,
          medproFee: paymentInfo.medproFee,
          transferFee: paymentInfo.transferFee,
          partnerImage: paymentInfo.partnerImage,
          expiredTime:router.query?.expiredTime
        }
      : {}
  }, [paymentInfo, getTranslatedText])

  // Loading state
  if (loading || translateLoading) {
    return (
      <div className={styles.paymentPage}>
        <div className={styles.loadingContainer}>
          <Spin size='large' />
          <p>
            {loading && 'Đang tải thông tin thanh toán...'}
            {translateLoading && 'Đang tải ngôn ngữ...'}
          </p>
        </div>
      </div>
    )
  }

  // Render payment status indicator
  const renderPaymentStatus = () => {
    if (isCheckingStatus) {
      return (
        <Alert
          message="Đang kiểm tra trạng thái thanh toán"
          description="Hệ thống đang tự động kiểm tra trạng thái thanh toán của bạn..."
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )
    }

    if (isSuccess) {
      return (
        <Alert
          message="Thanh toán thành công!"
          description="Giao dịch của bạn đã được xử lý thành công."
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )
    }

    if (transactionStatus?.status === 1) {
      return (
        <Alert
          message="Đang chờ thanh toán"
          description="Vui lòng hoàn tất thanh toán để tiếp tục."
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )
    }

    return null
  }

  return (
    <div className={styles.paymentPage}>
      {/* Payment Status */}
      {renderPaymentStatus()}

      {/* Main Content */}
      <div className={styles.mainContent}>
        <Row gutter={[56, 56]} align='stretch'>
          <Col xs={24} lg={12} xl={12}>
            <LeftPanel />
          </Col>
          <Col xs={24} lg={12} xl={12} flex={1}>
            {paymentData && Object.keys(paymentData).length > 0 ? (
              <PaymentCard
                payment={{...paymentData, ...paymentParams}}
                selectedTitle={translateData}
                renderListBanking={null}
                isMobile={false}
                ListLanguage={[]}
                selectLanguage={null}
                setSelectLanguage={() => {}}
                setDoMore={setDoMore}
                openSuccessModal={openSuccessModal}
                setOpenSuccessModal={setOpenSuccessModal}
              />
            ) : (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '400px'
                }}
              >
                <Spin size='large' />
              </div>
            )}
          </Col>
        </Row>
      </div>

      {/* Success Modal */}
      <Modal
        open={openSuccessModal.isOpen}
        onCancel={() => setOpenSuccessModal({ isOpen: false, redirectUrl: '' })}
        footer={[
          <Button
            key="ok"
            type="primary"
            onClick={() => {
              if (openSuccessModal.redirectUrl) {
                window.location.href = openSuccessModal.redirectUrl
              } else {
                router.push('/')
              }
            }}
          >
            Đồng ý
          </Button>
        ]}
        centered
        closable={false}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <CheckCircleOutlined
            style={{
              fontSize: '64px',
              color: '#52c41a',
              marginBottom: '16px'
            }}
          />
          <h2>Thanh toán thành công!</h2>
          <p>Giao dịch của bạn đã được xử lý thành công.</p>
          {transactionStatus && (
            <p style={{ fontSize: '12px', color: '#666' }}>
              Mã giao dịch: {transactionId}
            </p>
          )}
        </div>
      </Modal>
    </div>
  )
}

export default ThanhToanPage
