import { Col, message, Row, Spin } from 'antd'
import { useRouter } from 'next/router'
import React, { useEffect, useMemo, useState } from 'react'
import LeftPanel from '../../components/LeftPanel'
import PaymentCard from '../../components/PaymentCard'
import usePayment from '../../hooks/usePayment'
import { useTransactionStatus } from '../../hooks/useTransactionStatus'
import { GENERATE_QRCODE_LINK, useWindowResize } from '../../utils/constants'
import styles from './styles.module.less'
import Image from 'next/image'

export function ThanhToanPage() {
  const router = useRouter()
  const token = router.query.token as string
  // State for UI components
  const [doMore, setDoMore] = useState(true)
  const [openSuccessModal, setOpenSuccessModal] = useState(f)
  const isMobile = useWindowResize(576)

  const {
    paymentInfo,
    loading,
    translateData,
    translateLoading,
    getPaymentInfo,
    getTranslate,
    resetState
  } = usePayment()

  const paymentParams = useMemo(() => {
    if (!paymentInfo) return null

    return {
      ...paymentInfo,
      feeCode: paymentInfo.booking?.bookingCode || paymentInfo.feeCode,
      customerName: paymentInfo.user?.fullname || paymentInfo.hospitalName,
      amount:
        paymentInfo.payment?.amount ||
        paymentInfo.care247?.addonServices?.[0]?.price ||
        0,
      bankCode: paymentInfo.bankInfo?.bankCode,
      bankName: paymentInfo.bankInfo?.bankName,
      accountName: paymentInfo.bankInfo?.accountName,
      accountNo: paymentInfo.bankInfo?.accountNo,
      transferCode: paymentInfo.booking?.bookingCode || paymentInfo.feeCode,
      subTotal: paymentInfo.payment?.subTotal || paymentInfo.subTotal || 0,
      totalFee: paymentInfo.payment?.totalFee || paymentInfo.totalFee || 0,
      transferFee:
        paymentInfo.payment?.transferFee || paymentInfo.transferFee || 0,
      partnerImage: paymentInfo.partnerImage,
      expiredTime: paymentInfo.bankInfo?.expiredTime,
      qrCodeUrl: `${GENERATE_QRCODE_LINK}/${paymentInfo.bankInfo?.transactionId}/${paymentInfo.bankInfo?.extendData}`,
      fullname: paymentInfo?.user?.fullname,
      serviceName: paymentInfo?.care247?.name
    }
  }, [paymentInfo])

  // Use transaction status hook
  const { transactionStatus, isCheckingStatus, isSuccess, stopChecking } =
    useTransactionStatus({
      transactionId: paymentInfo?.payment?.transactionId || '',
      params: paymentParams,
      enabled: !!(
        paymentInfo?.payment?.transactionId &&
        paymentParams &&
        doMore
      ),
      onSuccess: (status) => {
        try {
          setOpenSuccessModal(true)
        } catch (error) {
          message.error('Có lỗi xảy ra khi xử lý kết quả thanh toán')
        }
      },
      onError: (error) => {
        try {
          message.error(`Lỗi kiểm tra trạng thái: ${error}`)
        } catch (err) {
          message.error('Có lỗi xảy ra khi kiểm tra trạng thái thanh toán')
        }
      }
    })
  // Fetch translate data using saga
  useEffect(() => {
    getTranslate({ language: 'vi' })
  }, [getTranslate])

  // Fetch payment info when component mounts
  useEffect(() => {
    getPaymentInfo({
      token: token
    })
  }, [token, getPaymentInfo])

  // Handle countdown expiration
  const handleCountdownExpired = () => {
    setDoMore(false)
    stopChecking()
    message.warning('Thời gian thanh toán đã hết!')
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      resetState()
      stopChecking()
    }
  }, [resetState, stopChecking])

  // Loading state
  if (loading || translateLoading) {
    return (
      <div className={styles.paymentPage}>
        <div className={styles.loadingContainer}>
          <Spin size='large' />
          <p>
            {loading && 'Đang tải thông tin thanh toán...'}
            {translateLoading && 'Đang tải ngôn ngữ...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.paymentPage}>
      {/* Main Content */}
      <div className={styles.care247Image}>
        <Image
          src='https://cdn.medpro.vn/prod-partner/cb7e19ae-37bf-4b95-8d6e-a894f447f7b9-frame_1000003180.png'
          alt='Patient Care'
          width={128}
          priority
          objectFit='contain'
          layout='fixed'
          height={47}
          className={styles.imageElement}
        />
      </div>
      <div className={styles.mainContent}>
        <Row gutter={[56, 56]} align='stretch' className={styles.rowContent}>
          <Col xs={24} lg={0} xl={12}>
            <LeftPanel />
          </Col>
          <Col xs={24} lg={24} xl={12} flex={1}>
            {paymentParams && Object.keys(paymentParams).length > 0 ? (
              <PaymentCard
                payment={paymentParams}
                selectedTitle={translateData}
                isMobile={isMobile}
                selectLanguage={null}
                setDoMore={setDoMore}
                openSuccessModal={openSuccessModal}
                setOpenSuccessModal={setOpenSuccessModal}
              />
            ) : (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '400px'
                }}
              >
                <Spin size='large' />
              </div>
            )}
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ThanhToanPage
