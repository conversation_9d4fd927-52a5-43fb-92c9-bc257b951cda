.container {
  // padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  @media (max-width: 576px) {
    box-shadow: none !important;
  }
  font-family: Roboto;

  .warringContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fef2f1;
    gap: 24px;
    padding: 22px 16px;
    @media (max-width: 576px) {
      flex-direction: column;
      gap: 12px;
      background-color: #fff;
      padding: 0px;
      border-radius: 16px;
    }
    .countDownt {
      @media (max-width: 576px) {
        width: 100%;
        display: flex !important;
        align-items: center;
        gap: 12px;
        justify-content: left;
        padding: 0 16px;
      }
    }
    .countdownTitle {
      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
      vertical-align: middle;
      white-space: nowrap;
      margin-bottom: 5px;
      @media (max-width: 576px) {
        margin-bottom: 0;
      }
    }
    .warringLeft {
      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
      vertical-align: bottom;
      color: #e25144;
      gap: 8px;
      display: flex;
      align-items: flex-start;
      @media (max-width: 576px) {
        background-color: #fef2f1;
        padding: 16px;
        margin-bottom: 12px;
        border-radius: 16px;
      }
    }
    .warningText {
      font-weight: 400;
      font-size: 16px;
      vertical-align: bottom;
      color: #e25144;
    }
  }
  .mobileCopySection {
    margin-bottom: 0px;
  }
  .copySection {
    display: flex;
    align-items: start;
    gap: 8px;
    .copyText {
      text-align: right;
      color: #003553;
      font-size: 16px;
    }
    .copyIcon {
      min-width: 19px !important;
      min-height: 19px !important;
      align-items: center;
      display: flex;
      margin-top: 3px;
    }
  }
  .arrowIcon {
    min-width: 19px !important;
    min-height: 19px !important;
    align-items: center;
    display: flex;
    justify-content: center;
  }
  .noteTitle {
    font-family: Roboto;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #e25144;
  }
  .noteContent {
    font-family: Roboto;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    margin-bottom: 0;
    color: #000000;
  }
  .paymentTitle {
    font-family: Roboto;
    font-weight: 700;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: center;
    text-transform: uppercase;
    color: #01b5f1;
    margin-top: 22px;
  }
  .payment {
    padding: 22px;
    @media (max-width: 576px) {
      display: none !important;
    }
    :global {
      .ant-card {
        border: none !important;
        background-color: #f4f6fa;
        border-radius: 16px;
      }
      .ant-card-body {
        padding: 28px 16px;
      }
    }

    .titleInfo {
      text-transform: uppercase;
      color: #003553;
      font-family: Roboto;
    }
  }
  .qrSection {
    padding: 0 16px;
    .qrTitle {
      font-family: Roboto;
      font-weight: 500;
      font-size: 24px;
      line-height: 100%;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
      color: #003553;
    }
    .instructButton {
      font-family: Roboto;
      font-weight: 700;
      font-size: 18px;
      line-height: 100%;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
      text-decoration: underline;
      text-decoration-style: solid;
      color: #003553;
      display: flex;
      align-items: center;
      margin: 0 auto;
    }
  }
  .qrContainer {
    width: 270px;
    height: 270px;
    position: relative;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    margin-top: 24px;
    @media (min-width: 992px) {
      margin-bottom: 30px;
    }
    @media (max-width: 992px) {
      img {
        width: 256px !important;
        height: 256px !important;
      }
    }
  }
  .vietQrLogo {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    @media (max-width: 992px) {
      img {
        width: 115px !important;
        height: 37px !important;
      }
    }
  }
  .qrContainer::before,
  .qrContainer::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to right,
        #00b5f1 0%,
        #00b5f1 10%,
        transparent 10%,
        transparent 90%,
        #00b5f1 90%,
        #00b5f1 100%
      ),
      linear-gradient(
        to bottom,
        #00b5f1 0%,
        #00b5f1 10%,
        transparent 10%,
        transparent 90%,
        #00b5f1 90%,
        #00b5f1 100%
      );
    background-size: 100% 2px, 2px 100%;
    background-repeat: no-repeat;
  }

  .qrContainer::before {
    top: 0;
    left: 0;
  }

  .qrContainer::after {
    bottom: 0;
    right: 0;
    transform: rotate(180deg);
  }
  .qrActions {
    margin-top: 12px;
    display: flex;
    gap: 35px;
    justify-content: center;
    align-items: center;
    @media (min-width: 576px) {
      display: none;
    }
    .actionButton {
      color: #00b5f1;
      display: flex;
      gap: 4px;
      align-items: center;
      .iconShare {
        font-weight: normal !important;
        margin-top: -2px;
        margin-right: 2px;
      }
      span {
        text-decoration: underline;
        font-size: 16px;
        font-weight: 500;
        line-height: 18.75px;
        text-align: left;
      }
    }
  }
  .mainActions {
    margin-top: 24px;
    display: flex;
    width: 100%;
    gap: 8px;
    position: relative;
    margin-bottom: 22px;
    @media (max-width: 576px) {
      position: fixed;
      bottom: 0;
      z-index: 1;
      background-color: #fff;
      width: 100%;
      padding: 16px;
      display: flex;
      justify-content: center;
      margin: 0;
      left: 0;
    }
    .downloadButton {
      background-color: #00b5f1 !important;
      color: #fff !important;
      &:hover {
        background-color: #01a9e1 !important;
      }
      @media (max-width: 576px) {
        display: none !important;
      }
    }
    .cancelButton {
      background-color: #e3e7eb;
      @media (max-width: 576px) {
        background-color: #f5f7fa !important;
      }
      &:hover {
        border: 1px solid #d5dadf;
      }
    }
    .button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px;
      width: calc(100% / 2);
      gap: 8px;
      border-radius: 12px;
      font-weight: 500;
      height: 56px;
      border: 1px solid transparent;
      &:hover {
        cursor: pointer;
      }

      @media (max-width: 992px) {
        width: 100%;
      }
    }
  }
}
.paymentRow {
  padding: 30px;
  display: none !important;
  @media (max-width: 576px) {
    padding: 0;
    padding-top: 0;
    border-radius: 12px;
    display: block !important;
  }
  .infoPayment {
    background: #f5f7fa;
    padding: 28px 16px;
    gap: 24px;
    border-radius: 12px;
    &:last-child {
      margin-top: 16px;
      margin-bottom: 22px;
    }
    .detailHeader {
      .titleInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        margin-bottom: 12px;
        border-bottom: 1px solid #e3e7eb;
        padding-bottom: 12px;
      }

      .label {
        color: #003553;
        font-family: Roboto;
        font-weight: 700;
        font-size: 18px;
        line-height: 100%;
        letter-spacing: 0%;
        vertical-align: middle;
        text-transform: uppercase;
      }
    }
  }
}
