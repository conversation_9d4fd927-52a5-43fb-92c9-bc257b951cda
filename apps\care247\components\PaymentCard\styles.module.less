.container {
  // padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

  font-family: Roboto;
  .warringContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fef2f1;
    gap: 24px;
    padding: 22px 16px;
    .countdownTitle {
      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: middle;
      white-space: nowrap;
      margin-bottom: 5px;
    }
    .warringLeft {
      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: bottom;
      color: #e25144;
      gap: 8px;
      display: flex;
      align-items: flex-start;
    }
    .warningText {
      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: bottom;
      color: #e25144;
    }
  }
  .payment {
    padding: 22px;
    :global {
      .ant-card {
        border: none !important;
        background-color: #f4f6fa;
        border-radius: 16px;
      }
      .ant-card-body {
        padding: 28px 16px;
      }
    }
    .paymentTitle {
      font-family: Roboto;
      font-weight: 700;
      font-size: 24px;
      line-height: 100%;
      letter-spacing: 0%;
      text-align: center;
      text-transform: uppercase;
      color: #01b5f1;
      margin-bottom: 24px;
    }
    .orderInformation {
      text-transform: uppercase;
      color: #003553;
    }
  }
}
