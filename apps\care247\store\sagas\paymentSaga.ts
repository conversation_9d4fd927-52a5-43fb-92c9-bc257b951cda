import { all, call, fork, put, takeLatest, delay, select } from 'redux-saga/effects'
import { AxiosResponse } from 'axios'
import { message } from 'antd'
import moment from 'moment'
import client from '../../config/medproSdk'
import { currentEnv } from '../../config/envs'
import { paymentActions, selectCheckCount, selectIsCheckingStatus } from '../slices/paymentSlice'
import {
  timeHHMMSS,
  MAX_CHECK_COUNT,
  CHECK_INTERVAL,
  pushUrlWeb
} from '../../utils/constants'
import PaymentGatewayService from '../../services/paymentGatewayService'

// API function wrapper
function* callGetPaymentInfo(transactionId: string, partnerId: string) {
  return yield call(() =>
    client.booking.getPayment(
      { transactionId },
      {
        partnerid: partnerId
      }
    )
  )
}

// API function for translate
function* callGetTranslate(language: string) {
  return yield call(() => {
    const apiRoot = currentEnv.API_BE
    return fetch(`${apiRoot}/resource/get-translate?language=${language}`)
      .then(response => response.json())
  })
}

// API function for check transaction status using service with retry
function* callGetTransactionStatus(transactionId: string, isRecheck = false) {
  try {
    console.log('callGetTransactionStatus - Calling API with retry:', { transactionId, isRecheck })
    const result = yield call(PaymentGatewayService.getTransactionStatusWithRetry, transactionId, isRecheck, 2)
    console.log('callGetTransactionStatus - API success:', result)
    return result
  } catch (error) {
    console.error('callGetTransactionStatus - API call failed after retries, falling back to mock:', error)
    // Fallback to mock for development
    try {
      const mockResult = yield call(PaymentGatewayService.getTransactionStatusMock, transactionId)
      console.log('callGetTransactionStatus - Mock fallback success:', mockResult)
      return mockResult
    } catch (mockError) {
      console.error('callGetTransactionStatus - Even mock failed:', mockError)
      // Ultimate fallback
      return {
        status: 1,
        description: 'Fallback pending status',
        partnerId: 'unknown'
      }
    }
  }
}

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    const { transactionId, partnerId } = action.payload

    // Call real API
    const response: AxiosResponse = yield call(callGetPaymentInfo, transactionId, partnerId)

    const { data } = response
    yield put(paymentActions.getPaymentInfoSuccess(data))

  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))
  }
}

function* getTranslateSaga(action: any) {
  try {
    const { language } = action.payload

    // Call translate API
    const data = yield call(callGetTranslate, language)

    // Dispatch success action
    yield put(paymentActions.getTranslateSuccess(data))

  } catch (error: any) {
    const errorMessage = error?.message || 'Có lỗi xảy ra khi tải ngôn ngữ'
    yield put(paymentActions.getTranslateFailure(errorMessage))
  }
}

function* checkTransactionStatusSaga(action: any) {
  try {
    console.log('checkTransactionStatusSaga - Starting with action:', action.payload)

    const { transactionId, params } = action.payload
    const checkCount: number = yield select(selectCheckCount)
    const isChecking: boolean = yield select(selectIsCheckingStatus)

    console.log('checkTransactionStatusSaga - Current state:', {
      transactionId,
      checkCount,
      isChecking,
      params: params ? Object.keys(params) : 'null'
    })

    // Check if expired
    if (params?.expiredTime) {
      const timess = timeHHMMSS({
        now: moment(),
        then: moment(params?.expiredTime)
      })

      console.log('checkTransactionStatusSaga - Time check:', {
        expiredTime: params.expiredTime,
        timeRemaining: timess?.time,
        isExpired: timess?.time < 0
      })

      if (timess?.time < 0) {
        console.log('checkTransactionStatusSaga - Payment expired, stopping check')
        message.warning('Thời gian thanh toán đã hết ! vui lòng kiểm tra lại phương thức thanh toán')
        yield put(paymentActions.stopCheckingStatus())
        pushUrlWeb({ redirectUrl: params.redirectUrl })
        return
      }
    }

    // Call API using service - use recheck API if not first time
    const isRecheck = checkCount > 0
    console.log('checkTransactionStatusSaga - Calling API:', {
      transactionId,
      isRecheck,
      checkCount
    })

    const response = yield call(callGetTransactionStatus, transactionId, isRecheck)
    console.log('checkTransactionStatusSaga - API response:', response)

    // Response is already normalized by the service
    const { status, description, partnerId: responsePartnerId, data } = response

    // Log TransactionInfo if available
    if (data?.TransactionInfo) {
      console.log('checkTransactionStatusSaga - TransactionInfo details:', {
        transactionId: data.TransactionInfo.transactionId,
        partnerId: data.TransactionInfo.partnerId,
        status: data.TransactionInfo.status,
        message: data.TransactionInfo.message,
        gatewayMessage: data.TransactionInfo.gatewayMessage
      })
    }

    const transactionStatus = {
      status,
      description: description || 'Unknown status',
      partnerId: responsePartnerId || params.partnerId || 'unknown'
    }

    console.log('checkTransactionStatusSaga - Normalized transaction status:', transactionStatus)
    yield put(paymentActions.checkTransactionStatusSuccess(transactionStatus))

    // Handle different status cases
    switch (status) {
      case 1: {
        // Still pending - continue checking if not expired and under max count
        if (isChecking && checkCount < MAX_CHECK_COUNT) {
          yield put(paymentActions.incrementCheckCount())
          yield delay(CHECK_INTERVAL)
          yield put(paymentActions.checkTransactionStatus({ transactionId, params }))
        } else if (!isChecking && params?.expiredTime) {
          message.warning('Thời gian thanh toán đã hết ! vui lòng kiểm tra lại phương thức thanh toán')
          pushUrlWeb({ redirectUrl: params.redirectUrl })
        }
        break
      }
      case 2:
      case 3: {
        // Success - handle redirect
        yield put(paymentActions.stopCheckingStatus())

        if (transactionStatus.partnerId === "care247") {
          // Show success modal for care247
          // This should be handled in component via selector
          setTimeout(() => {
            pushUrlWeb({ redirectUrl: params.redirectUrl })
          }, 5000)
        } else {
          pushUrlWeb({ redirectUrl: params.redirectUrl })
        }
        break
      }
      default: {
        // Other status - continue checking
        if (isChecking && checkCount < MAX_CHECK_COUNT) {
          yield put(paymentActions.incrementCheckCount())
          yield delay(CHECK_INTERVAL)
          yield put(paymentActions.checkTransactionStatus({ transactionId, params }))
        }
        break
      }
    }

  } catch (error: any) {
    console.error('Error in checkTransactionStatusSaga:', error)

    let errorMessage = 'Có lỗi xảy ra khi kiểm tra trạng thái thanh toán'

    if (error?.message) {
      errorMessage = error.message
    } else if (error?.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    yield put(paymentActions.checkTransactionStatusFailure(errorMessage))

    // Stop checking on error
    yield put(paymentActions.stopCheckingStatus())
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfo, getPaymentInfoSaga)
}

function* watcherGetTranslate() {
  yield takeLatest(paymentActions.getTranslate, getTranslateSaga)
}

function* watcherCheckTransactionStatus() {
  yield takeLatest(paymentActions.checkTransactionStatus, checkTransactionStatusSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo),
    fork(watcherGetTranslate),
    fork(watcherCheckTransactionStatus)
  ])
}

export default paymentSaga
