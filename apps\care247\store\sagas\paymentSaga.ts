import { all, call, fork, put, takeLatest, delay, select } from 'redux-saga/effects'
import { AxiosResponse } from 'axios'
import { message } from 'antd'
import moment from 'moment'
import client from '../../config/medproSdk'
import { currentEnv } from '../../config/envs'
import { paymentActions, selectCheckCount, selectIsCheckingStatus } from '../slices/paymentSlice'
import {
  timeHHMMSS,
  GATEWAY_CHECK_STT_TRANSACTION,
  GATEWAY_RECHECK_STT_TRANSACTION,
  MAX_CHECK_COUNT,
  CHECK_INTERVAL,
  pushUrlWeb
} from '../../utils/constants'

// API function wrapper
function* callGetPaymentInfo(transactionId: string, partnerId: string) {
  return yield call(() =>
    client.booking.getPayment(
      { transactionId },
      {
        partnerid: partnerId
      }
    )
  )
}

// API function for translate
function* callGetTranslate(language: string) {
  return yield call(() => {
    const apiRoot = currentEnv.API_BE
    return fetch(`${apiRoot}/resource/get-translate?language=${language}`)
      .then(response => response.json())
  })
}

// API function for check transaction status
function* callGetTransactionStatus(apiUrl: string, transactionId: string) {
  return yield call(() => {
    const apiRoot = currentEnv.API_BE
    const url = `${apiRoot}${apiUrl}?t=${moment().unix()}`
    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        isCallback: 0,
        transactionId
      })
    }).then(response => response.json())
  })
}

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    const { transactionId, partnerId } = action.payload

    // Call real API
    const response: AxiosResponse = yield call(callGetPaymentInfo, transactionId, partnerId)

    const { data } = response
    yield put(paymentActions.getPaymentInfoSuccess(data))

  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))
  }
}

function* getTranslateSaga(action: any) {
  try {
    const { language } = action.payload

    // Call translate API
    const data = yield call(callGetTranslate, language)

    // Dispatch success action
    yield put(paymentActions.getTranslateSuccess(data))

  } catch (error: any) {
    const errorMessage = error?.message || 'Có lỗi xảy ra khi tải ngôn ngữ'
    yield put(paymentActions.getTranslateFailure(errorMessage))
  }
}

function* checkTransactionStatusSaga(action: any) {
  try {
    const { transactionId, params } = action.payload
    const checkCount: number = yield select(selectCheckCount)
    const isChecking: boolean = yield select(selectIsCheckingStatus)

    // Check if expired
    if (params?.expiredTime) {
      const timess = timeHHMMSS({
        now: moment(),
        then: moment(params?.expiredTime)
      })

      if (timess?.time < 0) {
        message.warning('Thời gian thanh toán đã hết ! vui lòng kiểm tra lại phương thức thanh toán')
        yield put(paymentActions.stopCheckingStatus())
        pushUrlWeb({ redirectUrl: params.redirectUrl })
        return
      }
    }

    // Determine which API to use
    const apiUrl = checkCount === 0 ? GATEWAY_CHECK_STT_TRANSACTION : GATEWAY_RECHECK_STT_TRANSACTION

    // Call API
    const response = yield call(callGetTransactionStatus, apiUrl, transactionId)

    const {
      data: {
        data: {
          TransactionInfo: { status, description, partnerId }
        }
      }
    } = response

    const transactionStatus = { status, description, partnerId }
    yield put(paymentActions.checkTransactionStatusSuccess(transactionStatus))

    // Handle different status cases
    switch (status) {
      case 1: {
        // Still pending - continue checking if not expired and under max count
        if (isChecking && checkCount < MAX_CHECK_COUNT) {
          yield put(paymentActions.incrementCheckCount())
          yield delay(CHECK_INTERVAL)
          yield put(paymentActions.checkTransactionStatus({ transactionId, params }))
        } else if (!isChecking && params?.expiredTime) {
          message.warning('Thời gian thanh toán đã hết ! vui lòng kiểm tra lại phương thức thanh toán')
          pushUrlWeb({ redirectUrl: params.redirectUrl })
        }
        break
      }
      case 2:
      case 3: {
        // Success - handle redirect
        yield put(paymentActions.stopCheckingStatus())

        if (partnerId === "care247") {
          // Show success modal for care247
          // This should be handled in component via selector
          setTimeout(() => {
            pushUrlWeb({ redirectUrl: params.redirectUrl })
          }, 5000)
        } else {
          pushUrlWeb({ redirectUrl: params.redirectUrl })
        }
        break
      }
      default: {
        // Other status - continue checking
        if (isChecking && checkCount < MAX_CHECK_COUNT) {
          yield put(paymentActions.incrementCheckCount())
          yield delay(CHECK_INTERVAL)
          yield put(paymentActions.checkTransactionStatus({ transactionId, params }))
        }
        break
      }
    }

  } catch (error: any) {
    const errorMessage = error?.message || 'Có lỗi xảy ra khi kiểm tra trạng thái thanh toán'
    yield put(paymentActions.checkTransactionStatusFailure(errorMessage))
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfo, getPaymentInfoSaga)
}

function* watcherGetTranslate() {
  yield takeLatest(paymentActions.getTranslate, getTranslateSaga)
}

function* watcherCheckTransactionStatus() {
  yield takeLatest(paymentActions.checkTransactionStatus, checkTransactionStatusSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo),
    fork(watcherGetTranslate),
    fork(watcherCheckTransactionStatus)
  ])
}

export default paymentSaga
