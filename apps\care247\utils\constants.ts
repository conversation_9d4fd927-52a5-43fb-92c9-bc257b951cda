import moment from "moment";

// Time utility functions
export const timeHHMMSS = ({ now, then }: { now: moment.Moment; then: moment.Moment }) => {
  const duration = moment.duration(then.diff(now));
  const time = duration.asSeconds();
  
  if (time <= 0) {
    return {
      time: 0,
      resolveTime: {
        hours: "00",
        minutes: "00",
        seconds: "00"
      }
    };
  }

  const hours = Math.floor(duration.asHours());
  const minutes = Math.floor(duration.asMinutes()) % 60;
  const seconds = Math.floor(duration.asSeconds()) % 60;

  const resolveTime = {
    hours: String(hours).padStart(2, '0'),
    minutes: String(minutes).padStart(2, '0'),
    seconds: String(seconds).padStart(2, '0')
  };

  return {
    time,
    resolveTime
  };
};

// Other constants can be added here
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  EXPIRED: 'expired'
};

export const COUNTDOWN_INTERVAL = 1000; // 1 second
// Format amount utility function
export const formatAmount = (number: number | string | null | undefined): string => {
  if (!number) return '0';
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};