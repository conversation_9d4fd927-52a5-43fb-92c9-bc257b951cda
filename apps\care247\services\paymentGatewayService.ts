import moment from 'moment';
import {
  PAYMENT_GATEWAY_BASE_URL,
  GATEWAY_CHECK_STT_TRANSACTION,
  GATEWAY_RECHECK_STT_TRANSACTION
} from '../utils/constants';

export interface TransactionStatusRequest {
  isCallback: number;
  transactionId: string;
}

export interface TransactionInfo {
  transactionId: string;
  orderId: string;
  userId: string | null;
  name: string | null;
  partnerId: string;
  tennantId: string | null;
  gatewayId: string;
  gatewayTransactionId: string;
  amount: number | null;
  description: string | null;
  status: number;
  message: string;
  createdAt: string | null;
  updatedAt: string | null;
  gatewayMessage: string;
  feeCode: string | null;
  billId: string | null;
  patientId: string | null;
  patientEmrno: string | null;
  typeId: string | null;
  paymentType: string | null;
  phone: string | null;
  callbackMedproStatus: string | null;
}

export interface ApiResponse {
  TransactionInfo: TransactionInfo;
}

export interface TransactionStatusResponse {
  status: number;
  description?: string;
  message?: string;
  partnerId?: string;
  data?: any;
}

export class PaymentGatewayService {
  private static baseURL = PAYMENT_GATEWAY_BASE_URL;

  // Cache để lưu description từ getTransactionStatus
  private static descriptionCache = new Map<string, string>();

  private static getHeaders(): HeadersInit {
    return {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Authorization': 'Bearer',
      'Connection': 'keep-alive',
      'Content-Type': 'application/json;charset=UTF-8',
      'Origin': 'https://payment-gateway-qrcode.medpro.com.vn',
      'Referer': 'https://payment-gateway-qrcode.medpro.com.vn/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-site',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"'
    };
  }

  private static async callAPI(endpoint: string, transactionId: string, isRecheck = false): Promise<TransactionStatusResponse> {
    const url = `${this.baseURL}${endpoint}?t=${moment().unix()}`;

    const requestBody: TransactionStatusRequest = {
      isCallback: 0,
      transactionId
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: any = await response.json();

      // Normalize response structure
      if (data?.TransactionInfo) {
        // Direct TransactionInfo structure (actual API response)
        const { TransactionInfo } = data;
        console.log('PaymentGatewayService - Received TransactionInfo:', TransactionInfo);

        let description = TransactionInfo.message || TransactionInfo.description || 'Unknown status';

        // Nếu là getTransactionStatus (không phải recheck) và có description, lưu vào cache
        if (!isRecheck && TransactionInfo.description) {
          console.log('PaymentGatewayService - Caching description for transaction:', transactionId, TransactionInfo.description);
          this.descriptionCache.set(transactionId, TransactionInfo.description);
        }

        // Nếu là recheck và không có description, sử dụng từ cache
        if (isRecheck && !TransactionInfo.description) {
          const cachedDescription = this.descriptionCache.get(transactionId);
          if (cachedDescription) {
            console.log('PaymentGatewayService - Using cached description for recheck:', cachedDescription);
            description = cachedDescription;
          }
        }

        return {
          status: TransactionInfo.status,
          description: description,
          partnerId: TransactionInfo.partnerId,
          data: data
        };
      } else if (data?.data?.data?.TransactionInfo) {
        // Nested structure (fallback)
        const { TransactionInfo } = data.data.data;
        return {
          status: TransactionInfo.status,
          description: TransactionInfo.message || TransactionInfo.description,
          partnerId: TransactionInfo.partnerId,
          data: data
        };
      } else if (data?.data) {
        // Direct data structure (fallback)
        return {
          status: data.data.status || 1,
          description: data.data.message || data.data.description || 'Unknown status',
          partnerId: data.data.partnerId,
          data: data
        };
      } else {
        // Ultimate fallback structure
        return {
          status: data?.status || 1,
          description: data?.message || data?.description || 'Unknown status',
          partnerId: data?.partnerId,
          data: data
        };
      }
    } catch (error) {
      console.error(`Error calling ${endpoint}:`, error);

      // Enhanced error handling
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.');
      } else if (error instanceof Error) {
        throw new Error(`Lỗi API: ${error.message}`);
      } else {
        throw new Error('Có lỗi không xác định xảy ra khi gọi API');
      }
    }
  }

  static async getTransactionStatus(transactionId: string): Promise<TransactionStatusResponse> {
    return PaymentGatewayService.callAPI(GATEWAY_CHECK_STT_TRANSACTION, transactionId, false);
  }

  static async reCheckTransaction(transactionId: string): Promise<TransactionStatusResponse> {
    return PaymentGatewayService.callAPI(GATEWAY_RECHECK_STT_TRANSACTION, transactionId, true);
  }

  // Method để clear cache description
  static clearDescriptionCache(transactionId?: string): void {
    if (transactionId) {
      console.log('PaymentGatewayService - Clearing description cache for transaction:', transactionId);
      this.descriptionCache.delete(transactionId);
    } else {
      console.log('PaymentGatewayService - Clearing all description cache');
      this.descriptionCache.clear();
    }
  }

  // Method để get cached description
  static getCachedDescription(transactionId: string): string | undefined {
    return this.descriptionCache.get(transactionId);
  }

  // Method for testing with mock data
  static async getTransactionStatusMock(transactionId: string, isRecheck = false): Promise<TransactionStatusResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock different responses based on transactionId
    if (transactionId.includes('SUCCESS')) {
      const description = isRecheck ?
        (this.getCachedDescription(transactionId) || 'Thanh toán thành công') :
        'Thanh toán thành công';

      // Cache description on first call
      if (!isRecheck) {
        this.descriptionCache.set(transactionId, description);
      }

      return {
        status: 2,
        description: description,
        partnerId: 'care247'
      };
    } else if (transactionId.includes('FAILED')) {
      const description = isRecheck ?
        (this.getCachedDescription(transactionId) || 'Thanh toán không thành công') :
        'Thanh toán không thành công';

      if (!isRecheck) {
        this.descriptionCache.set(transactionId, description);
      }

      return {
        status: 4,
        description: description,
        partnerId: 'care247'
      };
    } else if (transactionId.includes('binhthanhhcm')) {
      // Mock response matching actual API structure
      const description = isRecheck ?
        (this.getCachedDescription(transactionId) || 'Chuyển khoản đến Bệnh viện Bình Thạnh') :
        'Chuyển khoản đến Bệnh viện Bình Thạnh - Phí khám bệnh';

      if (!isRecheck) {
        this.descriptionCache.set(transactionId, description);
      }

      return {
        status: 1,
        description: description,
        partnerId: 'binhthanhhcm'
      };
    } else {
      // Default pending status
      const description = isRecheck ?
        (this.getCachedDescription(transactionId) || 'Transaction is pending') :
        'Transaction is pending - Chuyển khoản thanh toán';

      if (!isRecheck) {
        this.descriptionCache.set(transactionId, description);
      }

      return {
        status: 1,
        description: description,
        partnerId: 'care247'
      };
    }
  }

  // Fallback method with retry logic
  static async getTransactionStatusWithRetry(
    transactionId: string,
    isRecheck = false,
    maxRetries = 3
  ): Promise<TransactionStatusResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempt ${attempt}/${maxRetries} for ${isRecheck ? 'recheck' : 'check'} transaction: ${transactionId}`);

        const result = isRecheck
          ? await PaymentGatewayService.reCheckTransaction(transactionId)
          : await PaymentGatewayService.getTransactionStatus(transactionId);

        console.log(`Success on attempt ${attempt}:`, result);
        return result;

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${attempt} failed:`, error);

        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          console.log(`Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If all retries failed, throw the last error
    throw lastError || new Error('All retry attempts failed');
  }

  // Test method to verify service is working
  static async testService(): Promise<void> {
    try {
      console.log('Testing PaymentGatewayService...');

      // Test description cache functionality
      const testTxId = 'TEST_CACHE_123';

      // Clear cache first
      this.clearDescriptionCache(testTxId);

      // Test getTransactionStatus (should cache description)
      console.log('Testing getTransactionStatus (should cache description)...');
      const result1 = await PaymentGatewayService.getTransactionStatusMock(testTxId, false);
      console.log('getTransactionStatus test result:', result1);

      // Check if description was cached
      const cachedDesc = this.getCachedDescription(testTxId);
      console.log('Cached description:', cachedDesc);

      // Test reCheckTransaction (should use cached description)
      console.log('Testing reCheckTransaction (should use cached description)...');
      const result2 = await PaymentGatewayService.getTransactionStatusMock(testTxId, true);
      console.log('reCheckTransaction test result:', result2);

      // Verify descriptions match
      if (result1.description === result2.description) {
        console.log('✅ Description cache working correctly!');
      } else {
        console.log('❌ Description cache not working as expected');
        console.log('First call description:', result1.description);
        console.log('Recheck description:', result2.description);
      }

      console.log('PaymentGatewayService test completed successfully!');
    } catch (error) {
      console.error('PaymentGatewayService test failed:', error);
      throw error;
    }
  }
}

export default PaymentGatewayService;
