import moment from 'moment';
import {
  PAYMENT_GATEWAY_BASE_URL,
  GATEWAY_CHECK_STT_TRANSACTION,
  GATEWAY_RECHECK_STT_TRANSACTION
} from '../utils/constants';

export interface TransactionStatusRequest {
  isCallback: number;
  transactionId: string;
}

export interface TransactionStatusResponse {
  status: number;
  description?: string;
  message?: string;
  partnerId?: string;
  data?: any;
}

export class PaymentGatewayService {
  private static baseURL = PAYMENT_GATEWAY_BASE_URL;

  private static getHeaders(): HeadersInit {
    return {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Authorization': 'Bearer',
      'Connection': 'keep-alive',
      'Content-Type': 'application/json;charset=UTF-8',
      'Origin': 'https://payment-gateway-qrcode.medpro.com.vn',
      'Referer': 'https://payment-gateway-qrcode.medpro.com.vn/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-site',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"'
    };
  }

  private static async callAPI(endpoint: string, transactionId: string): Promise<TransactionStatusResponse> {
    const url = `${this.baseURL}${endpoint}?t=${moment().unix()}`;

    const requestBody: TransactionStatusRequest = {
      isCallback: 0,
      transactionId
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Normalize response structure
      if (data?.data?.data?.TransactionInfo) {
        // Original nested structure
        const { TransactionInfo } = data.data.data;
        return {
          status: TransactionInfo.status,
          description: TransactionInfo.description,
          partnerId: TransactionInfo.partnerId,
          data: data
        };
      } else if (data?.data) {
        // Direct data structure
        return {
          status: data.data.status || 1,
          description: data.data.description || data.data.message,
          partnerId: data.data.partnerId,
          data: data
        };
      } else {
        // Fallback structure
        return {
          status: data?.status || 1,
          description: data?.description || data?.message || 'Unknown status',
          partnerId: data?.partnerId,
          data: data
        };
      }
    } catch (error) {
      console.error(`Error calling ${endpoint}:`, error);
      throw error;
    }
  }

  static async getTransactionStatus(transactionId: string): Promise<TransactionStatusResponse> {
    return this.callAPI(GATEWAY_CHECK_STT_TRANSACTION, transactionId);
  }

  static async reCheckTransaction(transactionId: string): Promise<TransactionStatusResponse> {
    return this.callAPI(GATEWAY_RECHECK_STT_TRANSACTION, transactionId);
  }

  // Method for testing with mock data
  static async getTransactionStatusMock(transactionId: string): Promise<TransactionStatusResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock different responses based on transactionId
    if (transactionId.includes('SUCCESS')) {
      return {
        status: 2,
        description: 'Transaction completed successfully',
        partnerId: 'care247'
      };
    } else if (transactionId.includes('FAILED')) {
      return {
        status: 4,
        description: 'Transaction failed',
        partnerId: 'care247'
      };
    } else {
      // Default pending status
      return {
        status: 1,
        description: 'Transaction is pending',
        partnerId: 'care247'
      };
    }
  }
}

export default PaymentGatewayService;
