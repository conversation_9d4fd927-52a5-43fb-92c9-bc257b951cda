import React, { useState } from 'react';
import { Card, Button, Space, Typography, Alert, Divider } from 'antd';
import PaymentGatewayService from '../../services/paymentGatewayService';
import { useTransactionStatus } from '../../hooks/useTransactionStatus';

const { Title, Text } = Typography;

const DescriptionTest: React.FC = () => {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // Test với hook
  const {
    transactionStatus,
    isCheckingStatus,
    statusError
  } = useTransactionStatus({
    transactionId: 'TEST_DESCRIPTION_123',
    params: {
      transactionId: 'TEST_DESCRIPTION_123',
      expiredTime: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
      redirectUrl: '/',
      partnerId: 'care247'
    },
    enabled: false // Không auto start
  });

  const addResult = (test: string, result: any) => {
    const newResult = {
      test,
      result,
      timestamp: new Date().toLocaleTimeString(),
      hasUnknownStatus: JSON.stringify(result).includes('Unknown status')
    };
    setResults(prev => [...prev, newResult]);
  };

  const testServiceDescriptions = async () => {
    setLoading(true);
    setResults([]);

    try {
      // Test các transaction IDs khác nhau
      const testCases = [
        { id: 'TEST_SUCCESS_123', name: 'Success Case' },
        { id: 'TEST_FAILED_456', name: 'Failed Case' },
        { id: 'TT_binhthanhhcm_789', name: 'Hospital Case' },
        { id: 'RANDOM_TX_ID', name: 'Default Case' }
      ];

      for (const testCase of testCases) {
        // Clear cache trước
        PaymentGatewayService.clearDescriptionCache(testCase.id);

        // Test getTransactionStatus
        const result1 = await PaymentGatewayService.getTransactionStatusMock(testCase.id, false);
        addResult(`${testCase.name} - getTransactionStatus`, result1);

        // Test reCheckTransaction
        const result2 = await PaymentGatewayService.getTransactionStatusMock(testCase.id, true);
        addResult(`${testCase.name} - reCheckTransaction`, result2);
      }

      // Test với empty/null data
      const emptyResult = {
        status: 1,
        description: null,
        partnerId: 'test'
      };
      addResult('Empty Description Test', emptyResult);

    } catch (error: any) {
      addResult('ERROR', error.message);
    } finally {
      setLoading(false);
    }
  };

  const testFallbackScenarios = async () => {
    setLoading(true);
    setResults([]);

    try {
      // Test các fallback scenarios
      const scenarios = [
        {
          name: 'No Description Field',
          data: { TransactionInfo: { status: 1, message: 'Test message', partnerId: 'test' } }
        },
        {
          name: 'Empty Description',
          data: { TransactionInfo: { status: 1, description: '', message: 'Test message', partnerId: 'test' } }
        },
        {
          name: 'Null Description',
          data: { TransactionInfo: { status: 1, description: null, message: 'Test message', partnerId: 'test' } }
        },
        {
          name: 'No Message and Description',
          data: { TransactionInfo: { status: 1, partnerId: 'test' } }
        }
      ];

      for (const scenario of scenarios) {
        // Simulate processing như trong service
        const { TransactionInfo } = scenario.data;
        const processedResult = {
          status: TransactionInfo.status,
          description: TransactionInfo.description || TransactionInfo.message || 'Nội dung chuyển khoản',
          partnerId: TransactionInfo.partnerId
        };
        
        addResult(scenario.name, processedResult);
      }

    } catch (error: any) {
      addResult('ERROR', error.message);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1000px', margin: '0 auto' }}>
      <Title level={2}>Description Test</Title>
      <Text type="secondary">
        Test để verify rằng "Unknown status" đã được thay thế bằng nội dung phù hợp
      </Text>

      <Card title="Test Controls" style={{ marginTop: '24px' }}>
        <Space wrap>
          <Button 
            type="primary" 
            onClick={testServiceDescriptions}
            loading={loading}
          >
            Test Service Descriptions
          </Button>
          <Button 
            onClick={testFallbackScenarios}
            loading={loading}
          >
            Test Fallback Scenarios
          </Button>
          <Button 
            onClick={clearResults}
            disabled={loading}
          >
            Clear Results
          </Button>
        </Space>
      </Card>

      {/* Hook Status */}
      <Card title="Hook Status" style={{ marginTop: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Is Checking: </Text>
            <Text>{isCheckingStatus ? 'Yes' : 'No'}</Text>
          </div>
          {statusError && (
            <Alert message="Hook Error" description={statusError} type="error" showIcon />
          )}
          {transactionStatus && (
            <div>
              <Text strong>Hook Transaction Status: </Text>
              <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>
                {JSON.stringify(transactionStatus, null, 2)}
              </pre>
            </div>
          )}
        </Space>
      </Card>

      {/* Test Results */}
      <Card 
        title={`Test Results (${results.length})`}
        style={{ marginTop: '16px' }}
      >
        {results.length === 0 ? (
          <Text type="secondary">No test results yet. Click a test button above.</Text>
        ) : (
          <Space direction="vertical" style={{ width: '100%' }}>
            {results.map((result, index) => (
              <div key={index}>
                <Alert
                  message={`${result.test} - ${result.timestamp}`}
                  description={
                    <div>
                      {result.hasUnknownStatus && (
                        <Alert 
                          message="⚠️ Contains 'Unknown status'" 
                          type="warning" 
                          size="small" 
                          style={{ marginBottom: '8px' }}
                        />
                      )}
                      <pre style={{ 
                        fontSize: '12px', 
                        margin: 0,
                        maxHeight: '150px',
                        overflow: 'auto'
                      }}>
                        {typeof result.result === 'object' 
                          ? JSON.stringify(result.result, null, 2)
                          : String(result.result)
                        }
                      </pre>
                    </div>
                  }
                  type={result.hasUnknownStatus ? 'warning' : 'success'}
                  showIcon
                />
                {index < results.length - 1 && <Divider style={{ margin: '8px 0' }} />}
              </div>
            ))}
          </Space>
        )}
      </Card>

      <Card title="Expected Behavior" style={{ marginTop: '16px' }}>
        <Space direction="vertical">
          <Text>
            <strong>✅ No "Unknown status":</strong> Tất cả descriptions should be meaningful
          </Text>
          <Text>
            <strong>✅ Fallback Values:</strong> "Nội dung chuyển khoản" thay vì "Unknown status"
          </Text>
          <Text>
            <strong>✅ Specific Descriptions:</strong> Mỗi partner/case có description phù hợp
          </Text>
          <Text>
            <strong>✅ Cache Consistency:</strong> Description nhất quán giữa getTransactionStatus và recheck
          </Text>
        </Space>
      </Card>

      <Card title="Description Examples" style={{ marginTop: '16px' }}>
        <Space direction="vertical">
          <Text><strong>Success:</strong> "Thanh toán thành công"</Text>
          <Text><strong>Failed:</strong> "Thanh toán không thành công"</Text>
          <Text><strong>Hospital:</strong> "Chuyển khoản thanh toán phí khám bệnh - Bệnh viện Bình Thạnh"</Text>
          <Text><strong>Default:</strong> "Chuyển khoản thanh toán dịch vụ y tế - Medpro"</Text>
          <Text><strong>Fallback:</strong> "Nội dung chuyển khoản"</Text>
        </Space>
      </Card>
    </div>
  );
};

export default DescriptionTest;
