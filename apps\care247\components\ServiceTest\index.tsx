import React, { useState } from 'react';
import { Card, Button, Space, Typography, Alert, message } from 'antd';
import PaymentGatewayService from '../../services/paymentGatewayService';

const { Title, Text } = Typography;

const ServiceTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const addResult = (test: string, success: boolean, data: any) => {
    const result = {
      test,
      success,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [result, ...prev]);
  };

  const testGetTransactionStatus = async () => {
    setLoading(true);
    try {
      console.log('Testing getTransactionStatus...');
      const result = await PaymentGatewayService.getTransactionStatus('TEST_123');
      console.log('getTransactionStatus result:', result);
      addResult('getTransactionStatus', true, result);
      message.success('getTransactionStatus test passed!');
    } catch (error) {
      console.error('getTransactionStatus failed:', error);
      addResult('getTransactionStatus', false, error);
      message.error('getTransactionStatus test failed!');
    } finally {
      setLoading(false);
    }
  };

  const testReCheckTransaction = async () => {
    setLoading(true);
    try {
      console.log('Testing reCheckTransaction...');
      const result = await PaymentGatewayService.reCheckTransaction('TEST_456');
      console.log('reCheckTransaction result:', result);
      addResult('reCheckTransaction', true, result);
      message.success('reCheckTransaction test passed!');
    } catch (error) {
      console.error('reCheckTransaction failed:', error);
      addResult('reCheckTransaction', false, error);
      message.error('reCheckTransaction test failed!');
    } finally {
      setLoading(false);
    }
  };

  const testWithRetry = async () => {
    setLoading(true);
    try {
      console.log('Testing getTransactionStatusWithRetry...');
      const result = await PaymentGatewayService.getTransactionStatusWithRetry('TEST_RETRY', false, 2);
      console.log('getTransactionStatusWithRetry result:', result);
      addResult('getTransactionStatusWithRetry (check)', true, result);
      message.success('getTransactionStatusWithRetry test passed!');
    } catch (error) {
      console.error('getTransactionStatusWithRetry failed:', error);
      addResult('getTransactionStatusWithRetry (check)', false, error);
      message.error('getTransactionStatusWithRetry test failed!');
    } finally {
      setLoading(false);
    }
  };

  const testWithRetryRecheck = async () => {
    setLoading(true);
    try {
      console.log('Testing getTransactionStatusWithRetry (recheck)...');
      const result = await PaymentGatewayService.getTransactionStatusWithRetry('TEST_RETRY_RECHECK', true, 2);
      console.log('getTransactionStatusWithRetry (recheck) result:', result);
      addResult('getTransactionStatusWithRetry (recheck)', true, result);
      message.success('getTransactionStatusWithRetry (recheck) test passed!');
    } catch (error) {
      console.error('getTransactionStatusWithRetry (recheck) failed:', error);
      addResult('getTransactionStatusWithRetry (recheck)', false, error);
      message.error('getTransactionStatusWithRetry (recheck) test failed!');
    } finally {
      setLoading(false);
    }
  };

  const testServiceMethod = async () => {
    setLoading(true);
    try {
      console.log('Testing PaymentGatewayService.testService()...');
      await PaymentGatewayService.testService();
      addResult('testService', true, 'Service test completed');
      message.success('Service test method passed!');
    } catch (error) {
      console.error('testService failed:', error);
      addResult('testService', false, error);
      message.error('Service test method failed!');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Service Test</Title>
      <Text type="secondary">
        Test để verify rằng PaymentGatewayService đã được sửa lỗi "Cannot read properties of null"
      </Text>

      <Card title="Test Controls" style={{ marginTop: '24px' }}>
        <Space wrap>
          <Button 
            type="primary" 
            onClick={testGetTransactionStatus}
            loading={loading}
          >
            Test getTransactionStatus
          </Button>
          <Button 
            onClick={testReCheckTransaction}
            loading={loading}
          >
            Test reCheckTransaction
          </Button>
          <Button 
            onClick={testWithRetry}
            loading={loading}
          >
            Test WithRetry (check)
          </Button>
          <Button 
            onClick={testWithRetryRecheck}
            loading={loading}
          >
            Test WithRetry (recheck)
          </Button>
          <Button 
            onClick={testServiceMethod}
            loading={loading}
          >
            Test Service Method
          </Button>
          <Button 
            onClick={clearResults}
            disabled={loading}
          >
            Clear Results
          </Button>
        </Space>
      </Card>

      <Card 
        title={`Test Results (${results.length})`}
        style={{ marginTop: '16px' }}
      >
        {results.length === 0 ? (
          <Text type="secondary">No test results yet. Click a test button above.</Text>
        ) : (
          <Space direction="vertical" style={{ width: '100%' }}>
            {results.map((result, index) => (
              <Alert
                key={index}
                message={`${result.test} - ${result.timestamp}`}
                description={
                  <pre style={{ 
                    fontSize: '12px', 
                    margin: 0,
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {typeof result.data === 'object' 
                      ? JSON.stringify(result.data, null, 2)
                      : String(result.data)
                    }
                  </pre>
                }
                type={result.success ? 'success' : 'error'}
                showIcon
              />
            ))}
          </Space>
        )}
      </Card>

      <Card title="Expected Behavior" style={{ marginTop: '16px' }}>
        <Space direction="vertical">
          <Text>
            <strong>✅ Success Case:</strong> Tất cả tests should pass và return mock data
          </Text>
          <Text>
            <strong>❌ Previous Error:</strong> "Cannot read properties of null (reading 'reCheckTransaction')"
          </Text>
          <Text>
            <strong>🔧 Fix Applied:</strong> Replaced "this.method()" with "PaymentGatewayService.method()"
          </Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Mở Console để xem detailed logs của từng test
          </Text>
        </Space>
      </Card>
    </div>
  );
};

export default ServiceTest;
