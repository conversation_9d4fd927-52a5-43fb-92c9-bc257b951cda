import type { AxiosResponse } from 'axios'

import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import type { ClientOptions, HeadersParams } from '../../interfaces'
import type { PaymentInfoQuery } from '../payment/interfaces'
import {
  BOOKING_GATEWAY_PAYMENT_FEE,
  BOOKING_SHARE_TO_PAY,
  CANCEL_RESERVATION_BOOKING,
  CHECK_TRANSACTION,
  CHORAY_EXAM_RESULT,
  CHORAY_GET_DETAIL_HEALTH_HISTORY,
  CHORAY_GET_HEALTH_EXAM_HISTORIES,
  CHORAY_GET_HEALTH_HISTORIES,
  COMPLAIN_BOOKING,
  DECODE_SHORT_LINK,
  GET_ALL_BOOKING_2,
  GET_ALL_BOOKING_BY,
  GET_ALL_BOOKING_BY_USER_ID,
  GET_ALL_CANCELLATION_REASON,
  GET_ALL_INVISIBLE_BOOKING,
  GET_ALL_INVISIBLE_BOOKING_BY_USER_ID,
  GET_BOOKING_UPDATE_HISTORY,
  GET_BOOKING_WITH_TRANSACTION_CODE,
  GET_BOOKINGS_AND_RESULTS,
  GET_PAYMENT,
  GET_PAYMENT_FEE_HISTORY,
  GET_PAYMENT_STATUS,
  HISTORY_EXAM,
  HISTORY_EXAM_BY_PATIENT,
  HISTORY_EXAM_BY_USER,
  HISTORY_EXAM_GROUP,
  HISTORY_EXAM_GROUP_DETAIL,
  INVISIBLE_BOOKING,
  MEDPRO_CARE_ADD_ON,
  MEDPRO_CARE_ADD_ON_CSKH,
  PAYMENT_FEE_TRACKING,
  RE_PAYMENT,
  RE_PAYMENT_NO_AUTH,
  RE_PAYMENT_SHARE_TO_PAY,
  RESERVE_BOOKING,
  RESERVE_MEDPRO_CARE,
  RESERVE_MEDPRO_CARE_ADDON,
  RESERVE_MEDPRO_CARE_ADDON_CSKH,
  RESERVE_MEDPRO_CARE_CSKH,
  RESERVE_MULTI_BOOKINGS,
  RETRY_SYNC_BOOKING_V1,
  SHARE_BOOKING,
  SHARE_BOOKING_INFO,
  TRACKING_MEDPRO_CARE,
  UPDATE_BOOKING,
  UPDATE_BOOKING_TELEMED_STATUS,
  UPDATE_BOOKING_TO_SHARE_PAYMENT,
  USER_ACCOUNT_CREATE_DEPOSIT,
  VALID_RE_EXAM_DATE,
  CANCELLEATION_REASON,
  CANCEL_BOOKING_V2,
  GET_BANK_LIST,
  GET_CARE247_INDEPENDENT
} from './constants'
import type {
  BookingShareToPayData,
  BookingTelemedQuery,
  BookingUpdateHistoryParam,
  CancelReservationBookingQuery,
  CheckTransactionData,
  ChoRayExamResultData,
  CreateDepositData,
  DetailHealthHistory,
  GetBookingsResults,
  HealthHistoriesByExamGroupId,
  HealthHistoriesByExamId,
  HealthHistoriesByPatientId,
  ICancelBooking,
  InvisibleBooking,
  PaymentFee,
  PaymentQuery,
  RePaymentQuery,
  ReserveBooking,
  ReserveMultipleBookingData,
  RetrySyncBookingV1,
  ShareBooking,
  ShareBookingInfo,
  UpdateBookingParam,
  UpdateBookingToSharePaymentData,
  ValidReExamDate
} from './interfaces'
import { TrackingMedproCareData } from './interfaces/tracking-medpro-care-data.interface'
import { BookingComplaintInterface } from './interfaces/booking-complain.interface'
import { ReserveMedproCare } from './interfaces/reserve-medpro-care.interface'
import { ReserveMedproCareAddon } from './interfaces/reserve-medpro-care-addon.interface'
import { MedproCareAddon } from './interfaces/medpro-care-addon.interface'

export interface IBooking {
  reserveBooking(
    data: ReserveBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  reserveMultipleBookings(
    data: ReserveMultipleBookingData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  cancelBooking(
    query: CancelReservationBookingQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  complainBooking(
    data: BookingComplaintInterface,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getAllBookings(headers?: HeadersParams): Promise<AxiosResponse>

  getAllInvisibleBookings(headers?: HeadersParams): Promise<AxiosResponse>

  getAllBookingByUserId(headers?: HeadersParams): Promise<AxiosResponse>

  getAllBooking(headers?: HeadersParams): Promise<AxiosResponse>

  getAllInvisibleBookingByUserId(
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  invisibleBooking(
    query: InvisibleBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getBookingWithTransactionCode(
    query: PaymentInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  checkTransaction(
    data: CheckTransactionData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  rePayment(
    query: RePaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  rePaymentShareToPay(
    query: RePaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  paymentFee(data: PaymentFee, headers?: HeadersParams): Promise<AxiosResponse>

  updateBookingTelemedStatus(
    data: BookingTelemedQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  paymentFeeTracking(headers?: HeadersParams): Promise<AxiosResponse>

  examResult(
    data: ChoRayExamResultData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getHealthHistories(
    data: HealthHistoriesByPatientId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getHealthExamHistories(
    data: HealthHistoriesByExamId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getDetailHealthHistory(
    data: DetailHealthHistory,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  createDeposit(
    data: CreateDepositData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  bookingShareToPay(
    data: BookingShareToPayData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  reserveMedproCareCSKH(
    data: ReserveMedproCare,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  reserveMedproCareAddonCSKH(
    data: ReserveMedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  rePaymentNoAuth(
    query: RePaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getHistoryExamByUser(headers?: HeadersParams): Promise<AxiosResponse>

  getHistoryExamByPatient(
    data: HealthHistoriesByPatientId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getHistoryExamByExamId(
    data: HealthHistoriesByExamId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  historyExamGroup(
    data: HealthHistoriesByPatientId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  historyExamGroupDetail(
    data: HealthHistoriesByExamGroupId,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getBookingsAndResults(
    data: GetBookingsResults,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updateBookingToSharePayment(
    data: UpdateBookingToSharePaymentData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getPaymentFeeHistory(headers?: HeadersParams): Promise<AxiosResponse>

  getPayment(
    query: PaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getCare247Independent(
    body: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  checkPaymentStatus(
    query: PaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  decodeShortLink(code: string, headers?: HeadersParams): Promise<AxiosResponse>

  updateBooking(
    data: UpdateBookingParam,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getBookingUpdateHistory(
    query: BookingUpdateHistoryParam,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getAllCancellationReason(headers?: HeadersParams): Promise<AxiosResponse>

  retrySyncBooking(
    data: RetrySyncBookingV1,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  validReExamDate(
    data: ValidReExamDate,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  trackingMedproCare(
    data: TrackingMedproCareData,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  reserveMedproCare(
    data: ReserveMedproCare,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getMedproCareAddon(
    body: MedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getMedproCareAddonCskh(
    body: MedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  reserveMedproCareAddon(
    data: ReserveMedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  shareBooking(
    body: ShareBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  shareBookingInfo(
    body: ShareBookingInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getCancelReasons(headers?: HeadersParams): Promise<AxiosResponse>

  cancelBookingV2(
    body: ICancelBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getBankList(headers?: HeadersParams): Promise<AxiosResponse>
}

export class Booking extends Base implements IBooking {
  constructor(options?: ClientOptions) {
    super(options)
  }

  reserveBooking(
    data: ReserveBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RESERVE_BOOKING),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  reserveMultipleBookings(
    data: ReserveMultipleBookingData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RESERVE_MULTI_BOOKINGS),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  cancelBooking(
    query: CancelReservationBookingQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CANCEL_RESERVATION_BOOKING, query),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  complainBooking(
    data: BookingComplaintInterface,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(COMPLAIN_BOOKING),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getAllBookings(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_ALL_BOOKING_2),
      Method.POST,
      { ...this.options, ...headers },
      {}
    )
  }

  getAllInvisibleBookings(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_ALL_INVISIBLE_BOOKING),
      Method.POST,
      { ...this.options, ...headers },
      {}
    )
  }

  getAllBookingByUserId(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_ALL_BOOKING_BY_USER_ID), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getAllBooking(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_ALL_BOOKING_BY), Method.POST, {
      ...this.options,
      ...headers
    })
  }

  getAllInvisibleBookingByUserId(
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_ALL_INVISIBLE_BOOKING_BY_USER_ID),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  invisibleBooking(
    query: InvisibleBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(INVISIBLE_BOOKING, query), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getBookingWithTransactionCode(
    query: PaymentInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_BOOKING_WITH_TRANSACTION_CODE, query),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  checkTransaction(
    data: CheckTransactionData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CHECK_TRANSACTION),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  rePayment(
    query: RePaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(RE_PAYMENT, query), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  paymentFee(
    data: PaymentFee,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(BOOKING_GATEWAY_PAYMENT_FEE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  updateBookingTelemedStatus(
    data: BookingTelemedQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_BOOKING_TELEMED_STATUS),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  paymentFeeTracking(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(PAYMENT_FEE_TRACKING),
      Method.POST,
      { ...this.options, ...headers },
      {}
    )
  }

  examResult(
    data: ChoRayExamResultData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CHORAY_EXAM_RESULT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getHealthHistories(
    data: HealthHistoriesByPatientId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CHORAY_GET_HEALTH_HISTORIES),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getHealthExamHistories(
    data: HealthHistoriesByExamId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CHORAY_GET_HEALTH_EXAM_HISTORIES),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getDetailHealthHistory(
    data: DetailHealthHistory,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CHORAY_GET_DETAIL_HEALTH_HISTORY),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  createDeposit(
    data: CreateDepositData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(USER_ACCOUNT_CREATE_DEPOSIT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  bookingShareToPay(
    data: BookingShareToPayData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(BOOKING_SHARE_TO_PAY),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  rePaymentShareToPay(
    body: RePaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RE_PAYMENT_SHARE_TO_PAY),
      Method.POST,
      { ...this.options, ...headers },
      body
    )
  }

  getHistoryExamByUser(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(HISTORY_EXAM_BY_USER), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getHistoryExamByPatient(
    data: HealthHistoriesByPatientId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(HISTORY_EXAM_BY_PATIENT),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getHistoryExamByExamId(
    data: HealthHistoriesByExamId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(HISTORY_EXAM),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  historyExamGroup(
    data: HealthHistoriesByPatientId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(HISTORY_EXAM_GROUP),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  historyExamGroupDetail(
    data: HealthHistoriesByExamGroupId,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(HISTORY_EXAM_GROUP_DETAIL),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getBookingsAndResults(
    data: GetBookingsResults,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_BOOKINGS_AND_RESULTS),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  updateBookingToSharePayment(
    data: UpdateBookingToSharePaymentData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_BOOKING_TO_SHARE_PAYMENT),
      Method.PATCH,
      { ...this.options, ...headers },
      data
    )
  }

  getPaymentFeeHistory(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_PAYMENT_FEE_HISTORY), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getCare247Independent(
    body?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_CARE247_INDEPENDENT),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  getPayment(
    query: PaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_PAYMENT, query), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  checkPaymentStatus(
    query: PaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_PAYMENT_STATUS, query), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  decodeShortLink(
    code: string,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(DECODE_SHORT_LINK, { code }), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  updateBooking(
    data: UpdateBookingParam,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_BOOKING),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getBookingUpdateHistory(
    query: BookingUpdateHistoryParam,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_BOOKING_UPDATE_HISTORY, query),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  getAllCancellationReason(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_ALL_CANCELLATION_REASON), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  retrySyncBooking(
    data: RetrySyncBookingV1,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RETRY_SYNC_BOOKING_V1),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  validReExamDate(
    data: ValidReExamDate,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(VALID_RE_EXAM_DATE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  trackingMedproCare(
    data: TrackingMedproCareData,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(TRACKING_MEDPRO_CARE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  reserveMedproCare(
    data: ReserveMedproCare,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RESERVE_MEDPRO_CARE),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getMedproCareAddon(
    body: MedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(MEDPRO_CARE_ADD_ON),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  getMedproCareAddonCskh(
    body: MedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(MEDPRO_CARE_ADD_ON_CSKH),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  reserveMedproCareAddon(
    data: ReserveMedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RESERVE_MEDPRO_CARE_ADDON),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  reserveMedproCareCSKH(
    data: ReserveMedproCare,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RESERVE_MEDPRO_CARE_CSKH),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  reserveMedproCareAddonCSKH(
    data: ReserveMedproCareAddon,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RESERVE_MEDPRO_CARE_ADDON_CSKH),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  rePaymentNoAuth(
    data: RePaymentQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(RE_PAYMENT_NO_AUTH),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  shareBooking(
    body: ShareBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(SHARE_BOOKING),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  shareBookingInfo(
    body: ShareBookingInfo,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(SHARE_BOOKING_INFO),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  getCancelReasons(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(CANCELLEATION_REASON), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  cancelBookingV2(
    body: ICancelBooking,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CANCEL_BOOKING_V2) + `/${body._id}`,
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  getBankList(headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      'https://api-medpro-care-app-beta.medpro.com.vn/' + GET_BANK_LIST,
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }
}
