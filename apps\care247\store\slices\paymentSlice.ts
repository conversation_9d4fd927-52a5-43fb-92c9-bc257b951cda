import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// Types
export interface PaymentInfo {
  appId: string
  partnerId: string
  type: number
  feeCode: string
  amount: number
  subTotal: number
  totalFee: number
  medproFee: number
  transferFee: number
  hospitalName: string
  partnerImage: string
}

export interface TranslateData {
  [key: string]: string
}

export interface TransactionStatus {
  status: number
  description: string
  partnerId: string
}

export interface PaymentState {
  paymentInfo: PaymentInfo | null
  translateData: TranslateData | null
  transactionStatus: TransactionStatus | null
  loading: boolean
  translateLoading: boolean
  statusLoading: boolean
  error: string | null
  translateError: string | null
  statusError: string | null
  checkCount: number
  isCheckingStatus: boolean
}

// Initial State
const initialState: PaymentState = {
  paymentInfo: null,
  translateData: null,
  transactionStatus: null,
  loading: false,
  translateLoading: false,
  statusLoading: false,
  error: null,
  translateError: null,
  statusError: null,
  checkCount: 0,
  isCheckingStatus: false
}

// Slice
const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    getPaymentInfo: (state, _action: PayloadAction<{
      transactionId: string
      partnerId: string
    }>) => {
      state.loading = true
      state.error = null
    },

    getPaymentInfoSuccess: (state, action: PayloadAction<PaymentInfo>) => {
      state.loading = false
      state.paymentInfo = action.payload
      state.error = null
    },

    getPaymentInfoFailure: (state, action: PayloadAction<string>) => {
      state.loading = false
      state.error = action.payload
      state.paymentInfo = null
    },

    getTranslate: (state, _action: PayloadAction<{
      language: string
    }>) => {
      state.translateLoading = true
      state.translateError = null
    },

    getTranslateSuccess: (state, action: PayloadAction<TranslateData>) => {
      state.translateLoading = false
      state.translateData = action.payload
      state.translateError = null
    },

    getTranslateFailure: (state, action: PayloadAction<string>) => {
      state.translateLoading = false
      state.translateError = action.payload
      state.translateData = null
    },

    clearPaymentError: (state) => {
      state.error = null
    },

    // Check transaction status actions
    checkTransactionStatus: (state, _action: PayloadAction<{
      transactionId: string
      params: any
    }>) => {
      state.statusLoading = true
      state.statusError = null
      state.isCheckingStatus = true
    },

    checkTransactionStatusSuccess: (state, action: PayloadAction<TransactionStatus>) => {
      state.statusLoading = false
      state.transactionStatus = action.payload
      state.statusError = null
    },

    checkTransactionStatusFailure: (state, action: PayloadAction<string>) => {
      state.statusLoading = false
      state.statusError = action.payload
      state.isCheckingStatus = false
    },

    incrementCheckCount: (state) => {
      state.checkCount += 1
    },

    stopCheckingStatus: (state) => {
      state.isCheckingStatus = false
      state.statusLoading = false
    },

    resetCheckCount: (state) => {
      state.checkCount = 0
    },

    resetPaymentState: (state) => {
      state.paymentInfo = null
      state.translateData = null
      state.transactionStatus = null
      state.loading = false
      state.translateLoading = false
      state.statusLoading = false
      state.error = null
      state.translateError = null
      state.statusError = null
      state.checkCount = 0
      state.isCheckingStatus = false
    }
  }
})

// Export actions
export const paymentActions = paymentSlice.actions

// Selectors
export const selectPaymentInfo = (state: any) => state.payment?.paymentInfo
export const selectPaymentLoading = (state: any) => state.payment?.loading
export const selectPaymentError = (state: any) => state.payment?.error
export const selectTranslateData = (state: any) => state.payment?.translateData
export const selectTranslateLoading = (state: any) => state.payment?.translateLoading
export const selectTranslateError = (state: any) => state.payment?.translateError
export const selectTransactionStatus = (state: any) => state.payment?.transactionStatus
export const selectStatusLoading = (state: any) => state.payment?.statusLoading
export const selectStatusError = (state: any) => state.payment?.statusError
export const selectCheckCount = (state: any) => state.payment?.checkCount
export const selectIsCheckingStatus = (state: any) => state.payment?.isCheckingStatus

// Export reducer
export default paymentSlice.reducer
