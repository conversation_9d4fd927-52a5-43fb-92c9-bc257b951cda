import { useRef, useState, useEffect } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/router'
import {
  But<PERSON>,
  Row,
  Col,
  message,
  Tooltip,
  Dropdown,
  Space,
  Card,
  Typography,
  Divider
} from 'antd'
import {
  DownloadOutlined,
  ShareAltOutlined,
  CopyOutlined,
  DownOutlined,
  UpOutlined,
  QuestionCircleOutlined,
  CloseOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import cx from 'classnames'

// Import your images - adjust paths according to your Next.js structure
import warringIcon from '../../public/svg/warning.svg'
import downLoadIcon from '../../public/svg/downLoad.svg'
import downLoadDesktopIcon from '../../public/svg/downLoadWhite.svg'
import vietQRIcon from '../../public/svg/vietQr.png'
import copyIcon from '../../public/svg/copyIcon.svg'
// Import your custom components
// import ModalHuongDanTaiApp from "../../Ebill/common/ModalHuongDanTaiApp";
// import ModalSuccessPayment from "../common/ModalSuccessPayment";
// import { formatAmount } from "../../../utils/func";
// import { pushUrlWeb } from "../common/func";
import styles from './styles.module.less'
import Countdown from '../Countdown/Countdown'
import { formatAmount } from '../../utils/constants'
import PaymentItem from '../PaymentItem/PaymentItem'
import { useTransactionStatus } from '../../hooks/useTransactionStatus'

const { Title, Text, Paragraph } = Typography

const PaymentCard = ({
  payment,
  renderListBanking,
  isMobile,
  ListLanguage,
  selectLanguage,
  setSelectLanguage,
  selectedTitle,
  setDoMore,
  openSuccessModal,
  setOpenSuccessModal,
}) => {
  const router = useRouter()
  const [openDetail, setOpenDetail] = useState(false)
  const [openPay, setOpenPay] = useState(false)
  const [basicModal, setBasicModal] = useState(false)

  // Use transaction status hook
  const {
    transactionStatus,
    isCheckingStatus,
    isSuccess,
    stopChecking
  } = useTransactionStatus({
    transactionId: payment?.transactionId,
    params: payment,
    enabled: !!(payment?.qrCodeUrl && payment?.transactionId && setDoMore),
    onSuccess: (status) => {
      // Handle successful payment
      if (status.partnerId === "care247") {
        setOpenSuccessModal({
          isOpen: true,
          redirectUrl: payment?.redirectUrl
        })
      }
    },
    onError: (error) => {
      console.error('Transaction status error:', error)
    }
  })
console.log('transactionStatus', transactionStatus)
  // Stop checking when doMore becomes false
  useEffect(() => {
    if (!setDoMore && isCheckingStatus) {
      stopChecking()
    }
  }, [setDoMore, isCheckingStatus, stopChecking])

  const toggleShow = () => setBasicModal(!basicModal)

  const toggleShowSuccess = () => {
    setOpenSuccessModal({
      ...openSuccessModal,
      isOpen: !openSuccessModal?.isOpen
    })
  }

  const downloadImage = async ({ url }) => {
    try {
      const image = await fetch(url)
      const imageBlog = await image.blob()
      const imageURL = URL.createObjectURL(imageBlog)
      const link = document.createElement('a')
      link.href = imageURL
      link.download = 'ma_thanh_toan'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(imageURL)
    } catch (error) {
      console.error('Error downloading image:', error)
      message.error('Lỗi khi tải xuống hình ảnh')
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        const response = await fetch(payment.qrCodeUrl)
        const blob = await response.blob()
        const file = new File([blob], 'image.jpg', { type: 'image/jpeg' })

        await navigator.share({
          title: 'Chia sẻ mã thanh toán!',
          text: 'Here is an amazing image I wanted to share with you.',
          files: [file]
        })

        console.log('Image shared successfully')
      } catch (error) {
        console.error('Error sharing:', error)
        downloadImage({ url: payment.qrCodeUrl })
      }
    } else {
      console.log(
        'Web Share API not supported. Providing a download link instead.'
      )
      downloadImage({ url: payment.qrCodeUrl })
    }
  }

  const onCopy = async (text) => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
        message.success('Sao chép thành công!')
      } else {
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-9999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
          message.success('Sao chép thành công!')
        } else {
          message.error('Không thể sao chép')
        }
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      message.error('Lỗi khi sao chép')
    }
  }

  const copyBank = () => {
    const text = payment.bankCode + ' - ' + payment.bankName
    onCopy(text)
  }

  const handleCancelPayment = () => {
    if (payment.redirectUrl) {
      router.push(payment.redirectUrl)
    } else {
      router.back()
    }
  }

  // Language dropdown items
  const languageItems = ListLanguage?.map((item) => ({
    key: item.id,
    label: (
      <div className={styles.languageItem}>
        <Image src={item.logo} alt={item.name} width={24} height={24} />
        <span>{item.name}</span>
      </div>
    ),
    onClick: () => setSelectLanguage(item)
  }))

  return (
    <div className={styles.container}>
      {/* Warning Section */}
      <div className={styles.warringContent}>
        <div className={styles.warringLeft}>
          <ExclamationCircleOutlined className={styles.warningIcon} />
          <Text className={styles.warningText}>
            {selectedTitle?.titleWaring}
          </Text>
        </div>
        <div className={styles.countDownt}>
          <div className={styles.countdownTitle}>
            {selectedTitle?.MedproBookingPaymentQR?.paymentDownTime}
          </div>
          <Countdown payment={payment} />
        </div>
      </div>
      <div className={styles.payment}>
        <div className={styles.paymentTitle}>
          Thanh toán chuyển khoản ngân hàng
        </div>
        <Card>
          <Title level={4} className={styles.titleInfo}>
            {selectedTitle?.MedproBookingPaymentQR?.orderInformation}
          </Title>

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.paymentAmount}
            value={`${formatAmount(payment?.amount)}đ`}
          />

          {payment?.feeCode && (
            <PaymentItem
              label={selectedTitle?.MedproBookingPaymentQR?.orderCode}
              value=''
            >
              <div className={styles.copySection}>
                <Text>{payment?.feeCode}</Text>
                <Image alt='copyIcon' src={copyIcon} width={18} height={18} />
              </div>
            </PaymentItem>
          )}

          <Title level={4} className={styles.titleInfo}>
            {selectedTitle?.MedproBookingPaymentQR?.transferInformation}
          </Title>
          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.bankName}
            value=''
          >
            <div className={styles.copySection}>
              <Text>{payment.bankCode + ' - ' + payment.bankName}</Text>
              <Image alt='copyIcon' src={copyIcon} width={18} height={18} />
            </div>
          </PaymentItem>

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.accountNo}
            value=''
          >
            <div className={styles.copySection}>
              <Text>{payment.accountNo}</Text>
              <Image alt='copyIcon' src={copyIcon} width={18} height={18} />
            </div>
          </PaymentItem>

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.accountName}
            value={payment.accountName}
          />

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.description}
            value={transactionStatus.description}
            className={styles.description}
          />

          <Card className={styles.noteCard} size='small'>
            <Title level={5} className={styles.noteTitle}>
              {selectedTitle?.MedproBookingPaymentQR?.note}
            </Title>
            <Paragraph className={styles.noteContent}>
              {selectedTitle?.MedproBookingPaymentQR?.noteAccount}
            </Paragraph>
          </Card>
        </Card>
      </div>
      {payment?.accountName && payment?.accountNo && (
        <Row gutter={[24, 24]} className={styles.paymentRow}>
          {/* Payment Information */}
          <Col xs={24} md={12} className={styles.infoPayment}></Col>

          {/* Mobile Payment Details */}
          <Col xs={24} md={0} className={styles.mobilePaymentDetails}>
            <div>
              <div
                className={styles.detailHeader}
                onClick={() => setOpenDetail(!openDetail)}
              >
                <Text strong>
                  {selectedTitle?.MedproBookingPaymentQR?.orderInformation}
                </Text>
                {openDetail ? <UpOutlined /> : <DownOutlined />}
              </div>

              <div className={styles.mobileAmount}>
                <Text code>{payment?.feeCode}</Text>
                <Button
                  type='text'
                  size='small'
                  icon={<CopyOutlined />}
                  onClick={() => onCopy(payment?.feeCode)}
                />
              </div>

              {openDetail && (
                <div className={styles.mobileDetails}>
                  <div className={styles.detailItem}>
                    <Text type='secondary'>
                      {selectedTitle?.MedproBookingPaymentQR?.paymentAmount}
                    </Text>
                    {/* <Text strong>{formatAmount(payment?.amount)}đ</Text> */}
                  </div>
                </div>
              )}
            </div>

            <div style={{ marginTop: 16 }}>
              <div
                className={styles.detailHeader}
                onClick={() => setOpenPay(!openPay)}
              >
                <Text strong>
                  {selectedTitle?.MedproBookingPaymentQR?.transferInformation}
                </Text>
                {openPay ? <UpOutlined /> : <DownOutlined />}
              </div>

              <div className={styles.mobileAmount}>
                <Text type='secondary'>
                  {selectedTitle?.MedproBookingPaymentQR?.accountNo}
                </Text>
                <div className={styles.accountSection}>
                  <Text code>{payment.accountNo}</Text>
                  <Button
                    type='text'
                    size='small'
                    icon={<CopyOutlined />}
                    onClick={() => onCopy(payment.accountNo)}
                  />
                </div>
              </div>

              {openPay && (
                <div className={styles.mobileDetails}>
                  <div className={styles.detailItem}>
                    <Text type='secondary'>
                      {selectedTitle?.MedproBookingPaymentQR?.bankName}
                    </Text>
                    <div className={styles.accountSection}>
                      <Text>{payment.bankCode + ' - ' + payment.bankName}</Text>
                      <Button
                        type='text'
                        size='small'
                        icon={<CopyOutlined />}
                        onClick={copyBank}
                      />
                    </div>
                  </div>
                  <div className={styles.detailItem}>
                    <Text type='secondary'>
                      {selectedTitle?.MedproBookingPaymentQR?.accountName}
                    </Text>
                    <Text>{payment?.accountName}</Text>
                  </div>
                  <div className={styles.detailItem}>
                    <Text type='secondary'>
                      {transactionStatus?.description}
                    </Text>
                    <Text>{payment?.description}</Text>
                  </div>
                  <Card className={styles.noteCard} size='small'>
                    <Title level={5} className={styles.noteTitle}>
                      {selectedTitle?.MedproBookingPaymentQR?.note}
                    </Title>
                    <Text className={styles.noteContent}>
                      {selectedTitle?.MedproBookingPaymentQR?.noteAccount}
                    </Text>
                  </Card>
                </div>
              )}
            </div>
          </Col>

          {/* QR Code Section */}
          <Col xs={24} md={12} className={styles.qrSection}>
            <div>
              <Title level={4} className={styles.qrTitle}>
                {selectedTitle?.MedproBookingPaymentQR?.titleQr}
              </Title>

              <Button
                type='link'
                icon={<QuestionCircleOutlined />}
                onClick={toggleShow}
                className={styles.instructButton}
              >
                {selectedTitle?.MedproBookingPaymentQR?.tutorialPayment}
              </Button>

              <div className={styles.qrContainer}>
                <div className={styles.vietQrLogo}>
                  <Image
                    alt='VietQR'
                    src={vietQRIcon}
                    width={108}
                    height={35}
                  />
                </div>
                <div className={styles.qrCode}>
                  <Image
                    alt='QR Code'
                    src={payment.qrCodeUrl}
                    width={250}
                    height={250}
                    unoptimized
                  />
                </div>
              </div>

              <div className={styles.qrActions}>
                <Button
                  type='text'
                  icon={<DownloadOutlined />}
                  onClick={() => downloadImage({ url: payment.qrCodeUrl })}
                  className={styles.actionButton}
                >
                  {selectedTitle?.MedproBookingPaymentQR?.downloadQr}
                </Button>
                <Button
                  type='text'
                  icon={<ShareAltOutlined />}
                  onClick={handleShare}
                  className={styles.actionButton}
                >
                  {selectedTitle?.MedproBookingPaymentQR?.shareQr}
                </Button>
              </div>

              <div className={styles.mainActions}>
                <Button
                  type='primary'
                  size='large'
                  icon={<DownloadOutlined />}
                  onClick={() => downloadImage({ url: payment.qrCodeUrl })}
                  className={styles.downloadButton}
                >
                  {selectedTitle?.MedproBookingPaymentQR?.downloadQr}
                </Button>
                <Button
                  size='large'
                  icon={<CloseOutlined />}
                  onClick={handleCancelPayment}
                  className={styles.cancelButton}
                >
                  {selectedTitle?.MedproBookingPaymentQR?.cancelPayment}
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      )}
      <div>
        <Title level={4}>
          {selectedTitle?.MedproBookingPaymentQR?.orderInformation}
        </Title>

        <div className={styles.paymentItem}>
          <Text type='secondary'>
            {selectedTitle?.MedproBookingPaymentQR?.totalAmount}
          </Text>
          <Title level={3} className={styles.amount}>
            {/* {formatAmount(payment?.amount)} */}
            <Text strong> VND</Text>
          </Title>
        </div>

        <div className={styles.paymentItem}>
          <Text type='secondary'>
            {selectedTitle?.MedproBookingPaymentQR?.valueTotal}
          </Text>
          <Text strong>
            {/* {formatAmount(payment?.subTotal)} */}
            <Text strong> VND</Text>
          </Text>
        </div>

        <div className={styles.paymentItem}>
          <Text type='secondary'>
            {selectedTitle?.MedproBookingPaymentQR?.fee}
          </Text>
          <Text strong>
            {/* {formatAmount(payment?.totalFee)} */}
            <Text strong> VND</Text>
          </Text>
        </div>

        {payment?.feeCode !== 0 && (
          <div className={styles.paymentItem}>
            <Text type='secondary'>
              {selectedTitle?.MedproBookingPaymentQR?.orderCode}
            </Text>
            <Text code>{payment?.feeCode}</Text>
          </div>
        )}

        {payment?.hospitalName && (
          <div className={styles.paymentItem}>
            <Text type='secondary'>
              {selectedTitle?.MedproBookingPaymentQR?.provider}
            </Text>
            <Text>{payment?.hospitalName}</Text>
          </div>
        )}
      </div>
      <div>
        <Text type='secondary'>{selectedTitle?.mobileTitlePay}</Text>
        <Title level={4} className={styles.mobileCode}>
          {payment?.feeCode}
        </Title>
        <div className={styles.mobileSummaryHeader}>
          <Title level={3} className={styles.mobileAmount}>
            {formatAmount(payment?.amount)}
            <Text strong> VND</Text>
          </Title>
          <Button
            type='link'
            onClick={() => setOpenDetail(!openDetail)}
            className={styles.detailButton}
          >
            {selectedTitle?.MedproBookingPaymentQR?.viewDetail}
            {openDetail ? <UpOutlined /> : <DownOutlined />}
          </Button>
        </div>

        {openDetail && (
          <div className={styles.mobileDetails}>
            <div className={styles.detailItem}>
              <Text type='secondary'>
                {selectedTitle?.MedproBookingPaymentQR?.valueTotal}
              </Text>
              <Text strong>
                {/* {formatAmount(payment?.subTotal)} */}
                <Text strong> VND</Text>
              </Text>
            </div>
            <div className={styles.detailItem}>
              <Text type='secondary'>
                {selectedTitle?.MedproBookingPaymentQR?.fee}
              </Text>
              <Text strong>
                {/* {formatAmount(payment?.totalFee)} */}
                <Text strong> VND</Text>
              </Text>
            </div>
            <div className={styles.detailItem}>
              <Text type='secondary'>
                {selectedTitle?.MedproBookingPaymentQR?.provider}
              </Text>
              <Text>{payment?.hospitalName}</Text>
            </div>
          </div>
        )}
      </div>
      <div>
        <Title level={4} className={styles.qrTitle}>
          {selectedTitle?.MedproBookingPaymentQR?.titleQr}
        </Title>

        <Button
          type='link'
          icon={<QuestionCircleOutlined />}
          onClick={toggleShow}
          className={styles.instructButton}
        >
          {selectedTitle?.MedproBookingPaymentQR?.tutorialPayment}
        </Button>

        <div className={styles.qrContainer}>
          <div className={styles.vietQrLogo}>
            <Image alt='VietQR' src={vietQRIcon} width={108} height={35} />
          </div>
          <div className={styles.qrCode}>
            <Image
              alt='QR Code'
              src={payment?.qrCodeUrl}
              width={250}
              height={250}
              unoptimized
            />
          </div>
        </div>

        <div className={styles.qrActions}>
          <Button
            type='text'
            icon={<DownloadOutlined />}
            onClick={() => downloadImage({ url: payment.qrCodeUrl })}
            className={styles.actionButton}
          >
            {selectedTitle?.MedproBookingPaymentQR?.downloadQr}
          </Button>
          <Button
            type='text'
            icon={<ShareAltOutlined />}
            onClick={handleShare}
            className={styles.actionButton}
          >
            {selectedTitle?.MedproBookingPaymentQR?.shareQr}
          </Button>
        </div>

        <div className={styles.mainActions}>
          <Button
            type='primary'
            size='large'
            icon={<DownloadOutlined />}
            onClick={() => downloadImage({ url: payment.qrCodeUrl })}
            className={styles.downloadButton}
          >
            {selectedTitle?.MedproBookingPaymentQR?.downloadQr}
          </Button>
          <Button
            size='large'
            icon={<CloseOutlined />}
            onClick={handleCancelPayment}
            className={styles.cancelButton}
          >
            {selectedTitle?.MedproBookingPaymentQR?.cancelPayment}
          </Button>
        </div>
      </div>

      {/* Modals
      {basicModal && (
        <ModalHuongDanTaiApp
          isOpen={basicModal}
          toggle={toggleShow}
          isMobile={isMobile}
          payment={payment}
          selectLanguage={selectLanguage}
          selectedTitle={selectedTitle}
        />
      )}

      {openSuccessModal?.isOpen && (
        <ModalSuccessPayment
          openSuccessModal={openSuccessModal}
          toggle={toggleShowSuccess}
          isMobile={isMobile}
          selectedTitle={selectedTitle}
        />
      )} */}
    </div>
  )
}

export default PaymentCard
