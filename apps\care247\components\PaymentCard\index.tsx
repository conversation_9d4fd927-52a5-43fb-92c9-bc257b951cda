import {
  CloseOutlined,
  DownloadOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  ShareAltOutlined,
  UpOutlined
} from '@ant-design/icons'
import { Button, Card, Divider, message, Typography } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

// Import your images - adjust paths according to your Next.js structure
import copyIcon from '../../public/svg/copyIcon.svg'
import vietQRIcon from '../../public/svg/vietQr.png'
// Import your custom components
// import ModalHuongDanTaiApp from "../../Ebill/common/ModalHuongDanTaiApp";
// import ModalSuccessPayment from "../common/ModalSuccessPayment";
// import { formatAmount } from "../../../utils/func";
// import { pushUrlWeb } from "../common/func";
import { useTransactionStatus } from '../../hooks/useTransactionStatus'
import { formatAmount } from '../../utils/constants'
import Countdown from '../Countdown/Countdown'
import ModalHuongDanTaiApp from '../ModalHuongDanTaiApp'
import PaymentItem from '../PaymentItem/PaymentItem'
import styles from './styles.module.less'

const { Title, Text, Paragraph } = Typography

const PaymentCard = ({
  payment,
  isMobile,
  selectLanguage,
  selectedTitle,
  setDoMore,
  openSuccessModal,
  setOpenSuccessModal
}) => {
  const router = useRouter()
  const [openDetail, setOpenDetail] = useState(false)
  const [openPay, setOpenPay] = useState(false)
  const [basicModal, setBasicModal] = useState(false)

  // Use transaction status hook
  const { transactionStatus, isCheckingStatus, isSuccess, stopChecking } =
    useTransactionStatus({
      transactionId: payment?.transactionId,
      params: payment,
      enabled: !!(payment?.qrCodeUrl && payment?.transactionId && setDoMore),
      onSuccess: (status) => {
        // Handle successful payment
        if (status.partnerId === 'care247') {
          setOpenSuccessModal({
            isOpen: true,
            redirectUrl: payment?.redirectUrl
          })
        }
      },
      onError: (error) => {
        // Handle error silently
      }
    })
  // Stop checking when doMore becomes false
  useEffect(() => {
    if (!setDoMore && isCheckingStatus) {
      stopChecking()
    }
  }, [setDoMore, isCheckingStatus, stopChecking])

  const toggleShow = () => setBasicModal(!basicModal)

  const toggleShowSuccess = () => {
    setOpenSuccessModal({
      ...openSuccessModal,
      isOpen: !openSuccessModal?.isOpen
    })
  }

  const downloadImage = async ({ url }) => {
    try {
      const image = await fetch(url)
      const imageBlog = await image.blob()
      const imageURL = URL.createObjectURL(imageBlog)
      const link = document.createElement('a')
      link.href = imageURL
      link.download = 'ma_thanh_toan'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(imageURL)
    } catch (error) {
      console.error('Error downloading image:', error)
      message.error('Lỗi khi tải xuống hình ảnh')
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        const response = await fetch(payment.qrCodeUrl)
        const blob = await response.blob()
        const file = new File([blob], 'image.jpg', { type: 'image/jpeg' })

        await navigator.share({
          title: 'Chia sẻ mã thanh toán!',
          text: 'Here is an amazing image I wanted to share with you.',
          files: [file]
        })

        console.log('Image shared successfully')
      } catch (error) {
        console.error('Error sharing:', error)
        downloadImage({ url: payment.qrCodeUrl })
      }
    } else {
      console.log(
        'Web Share API not supported. Providing a download link instead.'
      )
      downloadImage({ url: payment.qrCodeUrl })
    }
  }

  const onCopy = async (text) => {
    if (typeof text === 'object' && text !== null) {
      console.error('Received object instead of string:', text)
      // Try to find the text to copy from the clicked element
      if (text.currentTarget && text.currentTarget.previousSibling) {
        const textElement = text.currentTarget.previousSibling
        text = textElement.textContent || textElement.innerText || ''
      } else {
        message.error('Không thể xác định nội dung để sao chép')
        return
      }
    }

    if (!text) {
      message.error('Không có nội dung để sao chép')
      return
    }

    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
        message.success('Sao chép thành công!')
      } else {
        const textArea = document.createElement('textarea')
        textArea.value = text
        // Make the textarea out of viewport
        textArea.style.position = 'fixed'
        textArea.style.left = '-9999px'
        textArea.style.top = '0'
        textArea.style.pointerEvents = 'none'
        textArea.setAttribute('readonly', 'true')

        document.body.appendChild(textArea)

        // Handle iOS devices specifically
        if (navigator.userAgent.match(/ipad|iphone/i)) {
          const range = document.createRange()
          range.selectNodeContents(textArea)
          const selection = window.getSelection()
          selection.removeAllRanges()
          selection.addRange(range)
          textArea.setSelectionRange(0, text.length)
        } else {
          textArea.focus()
          textArea.select()
        }

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
          message.success('Sao chép thành công!')
        } else {
          message.error('Không thể sao chép')
        }
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      message.error('Lỗi khi sao chép')
    }
  }

  const copyBank = () => {
    const text = payment.bankCode + ' - ' + payment.bankName
    onCopy(text)
  }

  const handleCancelPayment = () => {
    if (payment.redirectUrl) {
      router.push(payment.redirectUrl)
    } else {
      router.back()
    }
  }

  return (
    <div className={styles.container}>
      {/* Warning Section */}
      <div className={styles.warringContent}>
        <div className={styles.warringLeft}>
          <ExclamationCircleOutlined className={styles.warningIcon} />
          <Text className={styles.warningText}>
            {selectedTitle?.titleWaring}
          </Text>
        </div>
        <div className={styles.countDownt}>
          <div className={styles.countdownTitle}>
            {selectedTitle?.MedproBookingPaymentQR?.paymentDownTime}
          </div>
          <Countdown payment={payment} />
        </div>
      </div>
      <div className={styles.paymentTitle}>
        Thanh toán chuyển khoản ngân hàng
      </div>
      <div className={styles.payment}>
        <Card>
          <Title level={4} className={styles.titleInfo}>
            {selectedTitle?.MedproBookingPaymentQR?.orderInformation}
          </Title>

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.paymentAmount}
            value={`${formatAmount(payment?.amount)}đ`}
          />
          {payment?.fullname && (
            <PaymentItem label={'Tên khách hàng'} value={payment?.fullname} />
          )}

          {payment?.feeCode && (
            <PaymentItem
              label={selectedTitle?.MedproBookingPaymentQR?.orderCode}
              value=''
            >
              <div className={styles.copySection}>
                <Text className={styles.copyText}>{payment?.feeCode}</Text>
                <div className={styles.copyIcon} onClick={onCopy}>
                  <Image alt='copyIcon' src={copyIcon} width={19} height={19} />
                </div>
              </div>
            </PaymentItem>
          )}
          {payment?.serviceName && (
            <PaymentItem label={'Tên dịch vụ'} value={payment?.serviceName} />
          )}

          <Title level={4} className={styles.titleInfo}>
            {selectedTitle?.MedproBookingPaymentQR?.transferInformation}
          </Title>
          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.bankName}
            value=''
          >
            <div className={styles.copySection}>
              <Text className={styles.copyText}>
                {payment.bankCode + ' - ' + payment.bankName}
              </Text>
              <div className={styles.copyIcon} onClick={copyBank}>
                <Image alt='copyIcon' src={copyIcon} width={19} height={19} />
              </div>
            </div>
          </PaymentItem>

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.accountNo}
            value=''
          >
            <div className={styles.copySection}>
              <Text className={styles.copyText}>{payment.accountNo}</Text>
              <div className={styles.copyIcon} onClick={onCopy}>
                <Image alt='copyIcon' src={copyIcon} width={19} height={19} />
              </div>
            </div>
          </PaymentItem>

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.accountName}
            value={payment.accountName}
          />

          <PaymentItem
            label={selectedTitle?.MedproBookingPaymentQR?.description}
            value={transactionStatus?.description || payment?.description}
            className={styles.description}
            isLast={true}
          />
          <Divider dashed />
          <div>
            <Title level={5} className={styles.noteTitle}>
              {selectedTitle?.MedproBookingPaymentQR?.note}
            </Title>
            <Paragraph className={styles.noteContent}>
              {selectedTitle?.MedproBookingPaymentQR?.noteAccount}
            </Paragraph>
          </div>
        </Card>
      </div>
      {payment?.accountName && payment?.accountNo && (
        <div className={styles.paymentRow} style={{ marginTop: 16 }}>
          <div className={styles.infoPayment}>
            <div className={styles.colapseContent}>
              <div
                className={styles.detailHeader}
                onClick={(e) => {
                  e.preventDefault()
                  setOpenDetail(!openDetail)
                }}
              >
                <div className={styles.titleInfo}>
                  <Text className={styles.label}>
                    {selectedTitle?.MedproBookingPaymentQR?.orderInformation}
                  </Text>
                  <div className={styles.arrowIcon}>
                    {openDetail ? <UpOutlined /> : <DownOutlined />}
                  </div>
                </div>
              </div>

              {payment?.feeCode && (
                <PaymentItem
                  label={selectedTitle?.MedproBookingPaymentQR?.orderCode}
                  value=''
                  isLast={!openDetail}
                >
                  <div className={cx(styles.copySection)}>
                    <Text className={styles.copyText}>{payment?.feeCode}</Text>
                    <div className={styles.copyIcon} onClick={onCopy}>
                      <Image
                        alt='copyIcon'
                        src={copyIcon}
                        width={19}
                        height={19}
                      />
                    </div>
                  </div>
                </PaymentItem>
              )}

              {openDetail && (
                <>
                  <PaymentItem
                    label={selectedTitle?.MedproBookingPaymentQR?.paymentAmount}
                    value={`${formatAmount(payment?.amount)}đ`}
                  />
                  {payment?.fullname && (
                    <PaymentItem
                      label={'Tên khách hàng'}
                      value={payment?.fullname}
                    />
                  )}
                  {payment?.serviceName && (
                    <PaymentItem
                      label={'Tên dịch vụ'}
                      value={payment?.serviceName}
                    />
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
      <div className={styles.paymentRow}>
        <div className={styles.infoPayment}>
          <div>
            <div
              className={styles.detailHeader}
              onClick={(e) => {
                e.stopPropagation()
                setOpenPay(!openPay)
              }}
            >
              <div className={styles.titleInfo}>
                <Text className={styles.label}>
                  {selectedTitle?.MedproBookingPaymentQR?.transferInformation}
                </Text>
                <div className={styles.arrowIcon}>
                  {openPay ? <UpOutlined /> : <DownOutlined />}
                </div>
              </div>
            </div>

            <div className={styles.mobileAmount}>
              <PaymentItem
                label={selectedTitle?.MedproBookingPaymentQR?.accountNo}
                value=''
              >
                <div className={styles.copySection}>
                  <Text className={styles.copyText}>{payment?.accountNo}</Text>
                  <div className={styles.copyIcon} onClick={onCopy}>
                    <Image
                      alt='copyIcon'
                      src={copyIcon}
                      width={19}
                      height={19}
                    />
                  </div>
                </div>
              </PaymentItem>
            </div>

            {openPay && (
              <div className={styles.mobileDetails}>
                <PaymentItem
                  label={selectedTitle?.MedproBookingPaymentQR?.bankName}
                  value=''
                  isLast={!openPay}
                >
                  <div className={styles.copySection}>
                    <Text className={styles.copyText}>
                      {payment.bankCode + ' - ' + payment.bankName}
                    </Text>
                    <div className={styles.copyIcon} onClick={copyBank}>
                      <Image
                        alt='copyIcon'
                        src={copyIcon}
                        width={19}
                        height={19}
                      />
                    </div>
                  </div>
                </PaymentItem>
                <PaymentItem
                  label={selectedTitle?.MedproBookingPaymentQR?.accountName}
                  value={payment?.accountName}
                />
                <PaymentItem
                  label={selectedTitle?.MedproBookingPaymentQR?.description}
                  value={transactionStatus?.description || payment?.description}
                  className={styles.description}
                  isLast={true}
                />
                <Divider dashed />
                <div>
                  <Title level={5} className={styles.noteTitle}>
                    {selectedTitle?.MedproBookingPaymentQR?.note}
                  </Title>
                  <Paragraph className={styles.noteContent}>
                    {selectedTitle?.MedproBookingPaymentQR?.noteAccount}
                  </Paragraph>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className={styles.qrSection}>
        <Title level={4} className={styles.qrTitle}>
          {selectedTitle?.MedproBookingPaymentQR?.titleQr}
        </Title>

        <Button
          type='link'
          icon={<QuestionCircleOutlined />}
          onClick={toggleShow}
          className={styles.instructButton}
        >
          {selectedTitle?.MedproBookingPaymentQR?.tutorialPayment}
        </Button>

        <div className={styles.qrContainer}>
          <div className={styles.vietQrLogo}>
            <Image alt='VietQR' src={vietQRIcon} width={108} height={35} />
          </div>
          <div className={styles.qrCode}>
            <Image
              alt='QR Code'
              src={payment?.qrCodeUrl}
              width={250}
              height={250}
              unoptimized
            />
          </div>
        </div>

        <div className={styles.qrActions}>
          <Button
            type='text'
            icon={<DownloadOutlined />}
            onClick={() => downloadImage({ url: payment.qrCodeUrl })}
            className={styles.actionButton}
          >
            {selectedTitle?.MedproBookingPaymentQR?.downloadQr}
          </Button>
          <Button
            type='text'
            icon={<ShareAltOutlined />}
            onClick={handleShare}
            className={styles.actionButton}
          >
            {selectedTitle?.MedproBookingPaymentQR?.shareQr}
          </Button>
        </div>

        <div className={styles.mainActions}>
          <Button
            type='primary'
            size='large'
            icon={<DownloadOutlined />}
            onClick={() => downloadImage({ url: payment.qrCodeUrl })}
            className={cx(styles.button, styles.downloadButton)}
          >
            {selectedTitle?.MedproBookingPaymentQR?.downloadQr}
          </Button>
          <Button
            size='large'
            icon={<CloseOutlined />}
            onClick={handleCancelPayment}
            className={cx(styles.button, styles.cancelButton)}
          >
            {selectedTitle?.MedproBookingPaymentQR?.cancelPayment}
          </Button>
        </div>
      </div>

      {basicModal && (
        <ModalHuongDanTaiApp
          isOpen={basicModal}
          toggle={toggleShow}
          isMobile={isMobile}
          // payment={payment}
          selectLanguage={selectLanguage}
          selectedTitle={selectedTitle}
        />
      )}
      {/* Modals
      {basicModal && (
        <ModalHuongDanTaiApp
          isOpen={basicModal}
          toggle={toggleShow}
          isMobile={isMobile}
          payment={payment}
          selectLanguage={selectLanguage}
          selectedTitle={selectedTitle}
        />
      )}

      {openSuccessModal?.isOpen && (
        <ModalSuccessPayment
          openSuccessModal={openSuccessModal}
          toggle={toggleShowSuccess}
          isMobile={isMobile}
          selectedTitle={selectedTitle}
        />
      )} */}
    </div>
  )
}

export default PaymentCard
