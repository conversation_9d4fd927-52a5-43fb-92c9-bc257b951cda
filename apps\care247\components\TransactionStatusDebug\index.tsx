import React, { useState } from 'react';
import { Card, Button, Input, Space, Typography, Alert, Divider } from 'antd';
import { useTransactionStatus } from '../../hooks/useTransactionStatus';

const { Title, Text } = Typography;

const TransactionStatusDebug: React.FC = () => {
  const [transactionId, setTransactionId] = useState('TT250620NNOJCMDLEAE1');
  const [enabled, setEnabled] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  // Mock payment params
  const mockParams = {
    transactionId,
    expiredTime: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes from now
    redirectUrl: '/',
    partnerId: 'care247',
    qrCodeUrl: 'https://example.com/qr.png',
    bankCode: 'VCB',
    bankName: 'Vietcombank',
    accountNo: '**********',
    accountName: 'NGUYEN VAN A',
    description: 'Thanh toan phi kham benh'
  };

  const {
    transactionStatus,
    isCheckingStatus,
    statusLoading,
    statusError,
    isSuccess,
    isPending,
    isFailed,
    checkCount,
    startChecking,
    stopChecking,
    resetStatus
  } = useTransactionStatus({
    transactionId,
    params: mockParams,
    enabled,
    onSuccess: (status) => {
      addLog(`✅ SUCCESS: ${JSON.stringify(status)}`);
    },
    onError: (error) => {
      addLog(`❌ ERROR: ${error}`);
    }
  });

  const handleStart = () => {
    addLog('🚀 Starting transaction status check...');
    setEnabled(true);
    startChecking();
  };

  const handleStop = () => {
    addLog('⏹️ Stopping transaction status check...');
    setEnabled(false);
    stopChecking();
  };

  const handleReset = () => {
    addLog('🔄 Resetting transaction status...');
    setEnabled(false);
    resetStatus();
    setLogs([]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1000px', margin: '0 auto' }}>
      <Title level={2}>Transaction Status Debug</Title>
      <Text type="secondary">
        Debug component để kiểm tra lỗi trong useTransactionStatus hook
      </Text>

      <Divider />

      <Card title="Controls" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Transaction ID:</Text>
            <Input
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Nhập transaction ID"
              style={{ marginTop: '8px' }}
              disabled={enabled}
            />
          </div>
          
          <Space>
            <Button 
              type="primary" 
              onClick={handleStart}
              disabled={enabled}
            >
              Start Checking
            </Button>
            <Button 
              onClick={handleStop}
              disabled={!enabled}
            >
              Stop Checking
            </Button>
            <Button onClick={handleReset}>
              Reset All
            </Button>
          </Space>
        </Space>
      </Card>

      <Card title="Current Status" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Enabled: </Text>
            <Text type={enabled ? 'success' : 'secondary'}>{enabled ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Is Checking: </Text>
            <Text type={isCheckingStatus ? 'warning' : 'secondary'}>{isCheckingStatus ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Status Loading: </Text>
            <Text type={statusLoading ? 'warning' : 'secondary'}>{statusLoading ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Check Count: </Text>
            <Text>{checkCount}</Text>
          </div>
          <div>
            <Text strong>Is Success: </Text>
            <Text type={isSuccess ? 'success' : 'secondary'}>{isSuccess ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Is Pending: </Text>
            <Text type={isPending ? 'warning' : 'secondary'}>{isPending ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Is Failed: </Text>
            <Text type={isFailed ? 'danger' : 'secondary'}>{isFailed ? 'Yes' : 'No'}</Text>
          </div>
          
          {statusError && (
            <Alert
              message="Status Error"
              description={statusError}
              type="error"
              showIcon
            />
          )}
          
          {transactionStatus && (
            <div>
              <Text strong>Transaction Status: </Text>
              <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>
                {JSON.stringify(transactionStatus, null, 2)}
              </pre>
            </div>
          )}
        </Space>
      </Card>

      <Card 
        title="Debug Logs" 
        extra={
          <Button size="small" onClick={clearLogs}>
            Clear Logs
          </Button>
        }
        style={{ marginBottom: '24px' }}
      >
        <div style={{ 
          background: '#000', 
          color: '#00ff00', 
          padding: '12px', 
          borderRadius: '4px',
          fontFamily: 'monospace',
          fontSize: '12px',
          maxHeight: '300px',
          overflowY: 'auto'
        }}>
          {logs.length === 0 ? (
            <Text style={{ color: '#666' }}>No logs yet...</Text>
          ) : (
            logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))
          )}
        </div>
      </Card>

      <Card title="Mock Params">
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '12px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto'
        }}>
          {JSON.stringify(mockParams, null, 2)}
        </pre>
      </Card>
    </div>
  );
};

export default TransactionStatusDebug;
