import React, { useEffect } from 'react';
import { useTransactionStatus } from '../../hooks/useTransactionStatus';
import { Alert, Spin, Typography } from 'antd';

const { Text } = Typography;

interface PaymentStatusExampleProps {
  transactionId: string;
  params: any;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

const PaymentStatusExample: React.FC<PaymentStatusExampleProps> = ({
  transactionId,
  params,
  onSuccess,
  onError
}) => {
  const {
    transactionStatus,
    isCheckingStatus,
    statusLoading,
    statusError,
    isSuccess,
    isPending,
    isFailed,
    checkCount
  } = useTransactionStatus({
    transactionId,
    params,
    enabled: true,
    onSuccess: (status) => {
      console.log('Payment successful:', status);
      if (onSuccess) onSuccess();
    },
    onError: (error) => {
      console.error('Payment error:', error);
      if (onError) onError(error);
    }
  });

  // Render status
  const renderStatus = () => {
    if (statusLoading || isCheckingStatus) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '10px' }}>
            <Text>Đang kiểm tra trạng thái thanh toán... (Lần {checkCount + 1})</Text>
          </div>
        </div>
      );
    }

    if (statusError) {
      return (
        <Alert
          message="Lỗi kiểm tra trạng thái"
          description={statusError}
          type="error"
          showIcon
        />
      );
    }

    if (isSuccess) {
      return (
        <Alert
          message="Thanh toán thành công!"
          description={`Mã giao dịch: ${transactionId}`}
          type="success"
          showIcon
        />
      );
    }

    if (isPending) {
      return (
        <Alert
          message="Đang chờ thanh toán"
          description="Vui lòng hoàn tất thanh toán để tiếp tục"
          type="info"
          showIcon
        />
      );
    }

    if (isFailed) {
      return (
        <Alert
          message="Thanh toán thất bại"
          description="Vui lòng thử lại hoặc chọn phương thức thanh toán khác"
          type="error"
          showIcon
        />
      );
    }

    return (
      <Alert
        message="Chưa có thông tin trạng thái"
        type="warning"
        showIcon
      />
    );
  };

  return (
    <div style={{ padding: '16px' }}>
      <h3>Trạng thái thanh toán</h3>
      {renderStatus()}
      
      {transactionStatus && (
        <div style={{ marginTop: '16px', fontSize: '12px', color: '#666' }}>
          <div>Status Code: {transactionStatus.status}</div>
          <div>Partner ID: {transactionStatus.partnerId}</div>
          <div>Description: {transactionStatus.description}</div>
        </div>
      )}
    </div>
  );
};

export default PaymentStatusExample;
