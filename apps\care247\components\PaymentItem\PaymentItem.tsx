import React from 'react';
import { Typography } from 'antd';
import styles from './styles.module.less';

const { Text, Title } = Typography;

interface PaymentItemProps {
  label: string;
  value: React.ReactNode;
  className?: string;
  isLast?: boolean;
  children?: React.ReactNode;
}

const PaymentItem: React.FC<PaymentItemProps> = ({
  label,
  value,
  className = '',
  isLast = false,
  children
}) => {
  const renderValue = () => {
    if (children) {
      return children;
    }

    return <Text className={styles.value}>{value}</Text>;
  };

  return (
    <div 
      className={`${styles.itemDetail} ${isLast ? styles.lastItemDetail : ''} ${className}`}
    >
      <Text type='secondary' className={styles.label}>{label}</Text>
      {renderValue()}
    </div>
  );
};

export default PaymentItem;
