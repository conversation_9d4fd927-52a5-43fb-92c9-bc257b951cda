import React, { useState } from 'react';
import { Card, Button, Input, Space, Typography, Alert, message, Divider } from 'antd';
import PaymentGatewayService from '../../services/paymentGatewayService';

const { Title, Text } = Typography;

const DescriptionCacheTest: React.FC = () => {
  const [transactionId, setTransactionId] = useState('TT_binhthanhhcm_TEST');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const addResult = (step: string, result: any) => {
    const newResult = {
      step,
      result,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [...prev, newResult]);
  };

  const testDescriptionCache = async () => {
    setLoading(true);
    setResults([]);

    try {
      // Step 1: Clear cache
      PaymentGatewayService.clearDescriptionCache(transactionId);
      addResult('1. Clear Cache', 'Cache cleared for transaction');

      // Step 2: First call - getTransactionStatus (should cache description)
      console.log('=== STEP 2: First call - getTransactionStatus ===');
      const firstResult = await PaymentGatewayService.getTransactionStatusMock(transactionId, false);
      addResult('2. getTransactionStatus (First Call)', {
        description: firstResult.description,
        cached: 'Description should be cached now'
      });

      // Step 3: Check cached description
      const cachedDesc = PaymentGatewayService.getCachedDescription(transactionId);
      addResult('3. Check Cache', {
        cachedDescription: cachedDesc || 'No cache found'
      });

      // Step 4: Second call - reCheckTransaction (should use cached description)
      console.log('=== STEP 4: Second call - reCheckTransaction ===');
      const secondResult = await PaymentGatewayService.getTransactionStatusMock(transactionId, true);
      addResult('4. reCheckTransaction (Should use cache)', {
        description: secondResult.description,
        note: 'Should be same as first call or use cached version'
      });

      // Step 5: Multiple recheck calls
      console.log('=== STEP 5: Multiple recheck calls ===');
      for (let i = 1; i <= 3; i++) {
        const recheckResult = await PaymentGatewayService.getTransactionStatusMock(transactionId, true);
        addResult(`5.${i}. Recheck Call ${i}`, {
          description: recheckResult.description,
          note: 'Should consistently use cached description'
        });
      }

      message.success('Description cache test completed!');

    } catch (error: any) {
      console.error('Description cache test failed:', error);
      addResult('ERROR', error.message);
      message.error('Test failed!');
    } finally {
      setLoading(false);
    }
  };

  const testDifferentTransactions = async () => {
    setLoading(true);
    setResults([]);

    try {
      const transactions = [
        'TT_SUCCESS_123',
        'TT_FAILED_456', 
        'TT_binhthanhhcm_789'
      ];

      for (const txId of transactions) {
        // Clear cache for this transaction
        PaymentGatewayService.clearDescriptionCache(txId);
        
        // First call
        const firstCall = await PaymentGatewayService.getTransactionStatusMock(txId, false);
        addResult(`${txId} - First Call`, {
          description: firstCall.description,
          partnerId: firstCall.partnerId
        });

        // Recheck call
        const recheckCall = await PaymentGatewayService.getTransactionStatusMock(txId, true);
        addResult(`${txId} - Recheck`, {
          description: recheckCall.description,
          partnerId: recheckCall.partnerId,
          same: firstCall.description === recheckCall.description ? 'YES' : 'NO'
        });
      }

      message.success('Multiple transactions test completed!');

    } catch (error: any) {
      console.error('Multiple transactions test failed:', error);
      addResult('ERROR', error.message);
      message.error('Test failed!');
    } finally {
      setLoading(false);
    }
  };

  const clearAllCache = () => {
    PaymentGatewayService.clearDescriptionCache();
    addResult('Clear All Cache', 'All description cache cleared');
    message.info('All cache cleared');
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1000px', margin: '0 auto' }}>
      <Title level={2}>Description Cache Test</Title>
      <Text type="secondary">
        Test để verify rằng description được cache từ getTransactionStatus và sử dụng lại trong reCheckTransaction
      </Text>

      <Card title="Test Controls" style={{ marginTop: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Transaction ID:</Text>
            <Input
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Nhập transaction ID"
              style={{ marginTop: '8px' }}
              disabled={loading}
            />
          </div>
          
          <Space wrap>
            <Button 
              type="primary" 
              onClick={testDescriptionCache}
              loading={loading}
            >
              Test Description Cache
            </Button>
            <Button 
              onClick={testDifferentTransactions}
              loading={loading}
            >
              Test Multiple Transactions
            </Button>
            <Button 
              onClick={clearAllCache}
              disabled={loading}
            >
              Clear All Cache
            </Button>
            <Button 
              onClick={clearResults}
              disabled={loading}
            >
              Clear Results
            </Button>
          </Space>
        </Space>
      </Card>

      <Card 
        title={`Test Results (${results.length} steps)`}
        style={{ marginTop: '16px' }}
      >
        {results.length === 0 ? (
          <Text type="secondary">No test results yet. Click a test button above.</Text>
        ) : (
          <Space direction="vertical" style={{ width: '100%' }}>
            {results.map((result, index) => (
              <div key={index}>
                <Alert
                  message={`${result.step} - ${result.timestamp}`}
                  description={
                    <pre style={{ 
                      fontSize: '12px', 
                      margin: 0,
                      maxHeight: '150px',
                      overflow: 'auto'
                    }}>
                      {typeof result.result === 'object' 
                        ? JSON.stringify(result.result, null, 2)
                        : String(result.result)
                      }
                    </pre>
                  }
                  type={result.step.includes('ERROR') ? 'error' : 'info'}
                  showIcon
                />
                {index < results.length - 1 && <Divider style={{ margin: '8px 0' }} />}
              </div>
            ))}
          </Space>
        )}
      </Card>

      <Card title="Expected Behavior" style={{ marginTop: '16px' }}>
        <Space direction="vertical">
          <Text>
            <strong>✅ getTransactionStatus (First Call):</strong> Trả về description đầy đủ và cache nó
          </Text>
          <Text>
            <strong>✅ reCheckTransaction (Subsequent Calls):</strong> Sử dụng cached description nếu API không trả về
          </Text>
          <Text>
            <strong>📝 Cache Logic:</strong> Description chỉ được cache từ getTransactionStatus, không từ recheck
          </Text>
          <Text>
            <strong>🔄 Consistency:</strong> Tất cả recheck calls sẽ có cùng description như first call
          </Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Mở Console để xem detailed logs về cache operations
          </Text>
        </Space>
      </Card>

      <Card title="Test Cases" style={{ marginTop: '16px' }}>
        <Space direction="vertical">
          <Text><strong>TT_SUCCESS_123:</strong> Success case với description cache</Text>
          <Text><strong>TT_FAILED_456:</strong> Failed case với description cache</Text>
          <Text><strong>TT_binhthanhhcm_TEST:</strong> Hospital partner với detailed description</Text>
          <Text><strong>Custom Transaction ID:</strong> Test với bất kỳ transaction ID nào</Text>
        </Space>
      </Card>
    </div>
  );
};

export default DescriptionCacheTest;
