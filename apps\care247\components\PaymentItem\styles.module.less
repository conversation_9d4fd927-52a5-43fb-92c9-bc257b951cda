.itemDetail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .lastItemDetail {
    margin-bottom: 0px !important;
  }

  .accountNo {
    display: flex;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18.75px;
    text-align: left;
  }
}

.value {
  font-family: <PERSON>o;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
  vertical-align: middle;
}
.label {
  font-family: Roboto;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
  vertical-align: middle;
}
