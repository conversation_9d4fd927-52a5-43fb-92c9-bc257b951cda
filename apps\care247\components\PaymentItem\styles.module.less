.itemDetail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  align-items: start;
  .accountNo {
    display: flex;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18.75px;
    text-align: left;
  }
}
.lastItemDetail {
  margin-bottom: 0px !important;
}

.value {
  font-family: <PERSON><PERSON>;
  font-weight: 400;
  font-size: 16px !important;
  line-height: 100%;
  text-align: right;
  color: #003553;
}
.label {
  font-family: Roboto;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  vertical-align: middle;
  min-width: 30%;
}
