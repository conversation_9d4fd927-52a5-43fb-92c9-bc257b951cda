import React, { useState } from 'react';
import { Card, Button, Input, Space, Typography, Alert, Spin, message } from 'antd';
import PaymentGatewayService from '../../services/paymentGatewayService';

const { Title, Text } = Typography;

const PaymentGatewayTest: React.FC = () => {
  const [transactionId, setTransactionId] = useState('TT250620NNOJCMDLEAE1');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTestAPI = async (isRecheck = false) => {
    if (!transactionId.trim()) {
      setError('Vui lòng nhập Transaction ID');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const result = isRecheck
        ? await PaymentGatewayService.reCheckTransaction(transactionId)
        : await PaymentGatewayService.getTransactionStatus(transactionId);
      setResponse({ ...result, apiType: isRecheck ? 'reCheckTransaction' : 'getTransactionStatus' });
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi gọi API');
    } finally {
      setLoading(false);
    }
  };

  const handleTestMockAPI = async () => {
    if (!transactionId.trim()) {
      setError('Vui lòng nhập Transaction ID');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const result = await PaymentGatewayService.getTransactionStatusMock(transactionId);
      setResponse(result);
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi gọi Mock API');
    } finally {
      setLoading(false);
    }
  };

  const renderStatusBadge = (status: number) => {
    switch (status) {
      case 1:
        return <Alert message="Pending" type="warning" showIcon style={{ display: 'inline-block' }} />;
      case 2:
      case 3:
        return <Alert message="Success" type="success" showIcon style={{ display: 'inline-block' }} />;
      default:
        return <Alert message="Failed" type="error" showIcon style={{ display: 'inline-block' }} />;
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Payment Gateway API Test</Title>
      <Text type="secondary">
        Test component để gọi API getTransactionStatus với cấu hình thực tế
      </Text>

      <Card title="API Test" style={{ marginTop: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Transaction ID:</Text>
            <Input
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Nhập transaction ID (VD: TT250620NNOJCMDLEAE1)"
              style={{ marginTop: '8px' }}
            />
          </div>
          
          <Space wrap>
            <Button
              type="primary"
              onClick={() => handleTestAPI(false)}
              loading={loading}
            >
              Test getTransactionStatus
            </Button>
            <Button
              type="default"
              onClick={() => handleTestAPI(true)}
              loading={loading}
            >
              Test reCheckTransaction
            </Button>
            <Button
              onClick={handleTestMockAPI}
              loading={loading}
            >
              Test Mock API
            </Button>
            <Button
              onClick={async () => {
                try {
                  await PaymentGatewayService.testService();
                  message.success('Service test completed successfully!');
                } catch (error) {
                  message.error('Service test failed!');
                }
              }}
              loading={loading}
            >
              Test Service
            </Button>
          </Space>

          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <strong>API Endpoints:</strong>
              <br />
              • getTransactionStatus: https://payment-gateway.medpro.com.vn/payment/v1/payin/getTransactionStatus
              <br />
              • reCheckTransaction: https://payment-gateway.medpro.com.vn/payment/v1/payin/reCheckTransaction
            </Text>
          </div>

          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <strong>Mock Transaction IDs:</strong>
              <br />
              • Chứa "SUCCESS" → Status 2 (Success)
              <br />
              • Chứa "FAILED" → Status 4 (Failed)
              <br />
              • Khác → Status 1 (Pending)
            </Text>
          </div>
        </Space>
      </Card>

      {loading && (
        <Card style={{ marginTop: '16px', textAlign: 'center' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>Đang gọi API...</Text>
          </div>
        </Card>
      )}

      {error && (
        <Alert
          message="Lỗi API"
          description={error}
          type="error"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}

      {response && (
        <Card title="API Response" style={{ marginTop: '16px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>API Type: </Text>
              <Text code>{response.apiType || 'Unknown'}</Text>
            </div>

            <div>
              <Text strong>Status: </Text>
              {renderStatusBadge(response.status)}
            </div>

            <div>
              <Text strong>Description: </Text>
              <Text>{response.description || 'N/A'}</Text>
            </div>

            <div>
              <Text strong>Partner ID: </Text>
              <Text>{response.partnerId || 'N/A'}</Text>
            </div>

            <div>
              <Text strong>Raw Response:</Text>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '300px'
              }}>
                {JSON.stringify(response, null, 2)}
              </pre>
            </div>
          </Space>
        </Card>
      )}

      <Card title="cURL Commands" style={{ marginTop: '16px' }}>
        <div>
          <Text strong>getTransactionStatus:</Text>
          <pre style={{
            background: '#f5f5f5',
            padding: '12px',
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto',
            marginTop: '8px'
          }}>
{`curl 'https://payment-gateway.medpro.com.vn/payment/v1/payin/getTransactionStatus?t=${Math.floor(Date.now() / 1000)}' \\
  -H 'Accept: application/json, text/plain, */*' \\
  -H 'Authorization: Bearer' \\
  -H 'Content-Type: application/json;charset=UTF-8' \\
  -H 'Origin: https://payment-gateway-qrcode.medpro.com.vn' \\
  -H 'Referer: https://payment-gateway-qrcode.medpro.com.vn/' \\
  --data-raw '{"isCallback":0,"transactionId":"${transactionId}"}'`}
          </pre>
        </div>

        <div style={{ marginTop: '16px' }}>
          <Text strong>reCheckTransaction:</Text>
          <pre style={{
            background: '#f5f5f5',
            padding: '12px',
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto',
            marginTop: '8px'
          }}>
{`curl 'https://payment-gateway.medpro.com.vn/payment/v1/payin/reCheckTransaction?t=${Math.floor(Date.now() / 1000)}' \\
  -H 'Accept: application/json, text/plain, */*' \\
  -H 'Authorization: Bearer' \\
  -H 'Content-Type: application/json;charset=UTF-8' \\
  -H 'Origin: https://payment-gateway-qrcode.medpro.com.vn' \\
  -H 'Referer: https://payment-gateway-qrcode.medpro.com.vn/' \\
  --data-raw '{"isCallback":0,"transactionId":"${transactionId}"}'`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default PaymentGatewayTest;
