import React, { useState } from 'react';
import { Card, Button, Input, Space, Typography, Alert, Spin } from 'antd';
import PaymentGatewayService from '../../services/paymentGatewayService';

const { Title, Text } = Typography;

const PaymentGatewayTest: React.FC = () => {
  const [transactionId, setTransactionId] = useState('TT250620NNOJCMDLEAE1');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTestAPI = async () => {
    if (!transactionId.trim()) {
      setError('Vui lòng nhập Transaction ID');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const result = await PaymentGatewayService.getTransactionStatus(transactionId);
      setResponse(result);
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi gọi API');
    } finally {
      setLoading(false);
    }
  };

  const handleTestMockAPI = async () => {
    if (!transactionId.trim()) {
      setError('Vui lòng nhập Transaction ID');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const result = await PaymentGatewayService.getTransactionStatusMock(transactionId);
      setResponse(result);
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi gọi Mock API');
    } finally {
      setLoading(false);
    }
  };

  const renderStatusBadge = (status: number) => {
    switch (status) {
      case 1:
        return <Alert message="Pending" type="warning" showIcon style={{ display: 'inline-block' }} />;
      case 2:
      case 3:
        return <Alert message="Success" type="success" showIcon style={{ display: 'inline-block' }} />;
      default:
        return <Alert message="Failed" type="error" showIcon style={{ display: 'inline-block' }} />;
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Payment Gateway API Test</Title>
      <Text type="secondary">
        Test component để gọi API getTransactionStatus với cấu hình thực tế
      </Text>

      <Card title="API Test" style={{ marginTop: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Transaction ID:</Text>
            <Input
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Nhập transaction ID (VD: TT250620NNOJCMDLEAE1)"
              style={{ marginTop: '8px' }}
            />
          </div>
          
          <Space>
            <Button 
              type="primary" 
              onClick={handleTestAPI}
              loading={loading}
            >
              Test Real API
            </Button>
            <Button 
              onClick={handleTestMockAPI}
              loading={loading}
            >
              Test Mock API
            </Button>
          </Space>

          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <strong>API Endpoint:</strong> https://payment-gateway.medpro.com.vn/payment/v1/payin/getTransactionStatus
            </Text>
          </div>

          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <strong>Mock Transaction IDs:</strong>
              <br />
              • Chứa "SUCCESS" → Status 2 (Success)
              <br />
              • Chứa "FAILED" → Status 4 (Failed)
              <br />
              • Khác → Status 1 (Pending)
            </Text>
          </div>
        </Space>
      </Card>

      {loading && (
        <Card style={{ marginTop: '16px', textAlign: 'center' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>Đang gọi API...</Text>
          </div>
        </Card>
      )}

      {error && (
        <Alert
          message="Lỗi API"
          description={error}
          type="error"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}

      {response && (
        <Card title="API Response" style={{ marginTop: '16px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>Status: </Text>
              {renderStatusBadge(response.status)}
            </div>
            
            <div>
              <Text strong>Description: </Text>
              <Text>{response.description || 'N/A'}</Text>
            </div>
            
            <div>
              <Text strong>Partner ID: </Text>
              <Text>{response.partnerId || 'N/A'}</Text>
            </div>

            <div>
              <Text strong>Raw Response:</Text>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '300px'
              }}>
                {JSON.stringify(response, null, 2)}
              </pre>
            </div>
          </Space>
        </Card>
      )}

      <Card title="cURL Command" style={{ marginTop: '16px' }}>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '12px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto'
        }}>
{`curl 'https://payment-gateway.medpro.com.vn/payment/v1/payin/getTransactionStatus?t=${Math.floor(Date.now() / 1000)}' \\
  -H 'Accept: application/json, text/plain, */*' \\
  -H 'Accept-Language: en-US,en;q=0.9' \\
  -H 'Authorization: Bearer' \\
  -H 'Connection: keep-alive' \\
  -H 'Content-Type: application/json;charset=UTF-8' \\
  -H 'Origin: https://payment-gateway-qrcode.medpro.com.vn' \\
  -H 'Referer: https://payment-gateway-qrcode.medpro.com.vn/' \\
  --data-raw '{"isCallback":0,"transactionId":"${transactionId}"}'`}
        </pre>
      </Card>
    </div>
  );
};

export default PaymentGatewayTest;
