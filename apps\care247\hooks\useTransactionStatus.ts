import { useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  paymentActions,
  selectTransactionStatus,
  selectIsCheckingStatus,
  selectCheckCount,
  selectStatusLoading,
  selectStatusError,
  TransactionStatus
} from '../store/slices/paymentSlice';

interface UseTransactionStatusProps {
  transactionId?: string;
  params?: {
    transactionId?: string;
    expiredTime?: string;
    redirectUrl?: string;
    partnerId?: string;
    [key: string]: any;
  };
  enabled?: boolean;
  onSuccess?: (status: TransactionStatus) => void;
  onError?: (error: string) => void;
}

export const useTransactionStatus = ({
  transactionId,
  params,
  enabled = true,
  onSuccess,
  onError
}: UseTransactionStatusProps) => {
  const dispatch = useDispatch();
  const hasStartedRef = useRef(false);

  // Selectors
  const transactionStatus = useSelector(selectTransactionStatus);
  const isCheckingStatus = useSelector(selectIsCheckingStatus);
  const checkCount = useSelector(selectCheckCount);
  const statusLoading = useSelector(selectStatusLoading);
  const statusError = useSelector(selectStatusError);

  // Memoize control functions
  const startChecking = useCallback(() => {
    if (transactionId && params) {
      dispatch(paymentActions.checkTransactionStatus({
        transactionId,
        params
      }));
      hasStartedRef.current = true;
    }
  }, [transactionId, params, dispatch]);

  const stopChecking = useCallback(() => {
    dispatch(paymentActions.stopCheckingStatus());
    hasStartedRef.current = false;
  }, [dispatch]);

  const resetStatus = useCallback(() => {
    dispatch(paymentActions.resetCheckCount());
    dispatch(paymentActions.stopCheckingStatus());
    hasStartedRef.current = false;
  }, [dispatch]);

  // Start checking when enabled and have required params
  useEffect(() => {
    if (enabled && transactionId && params && !hasStartedRef.current) {
      startChecking();
    } else if (!enabled && hasStartedRef.current) {
      stopChecking();
    }
  }, [enabled, transactionId, startChecking, stopChecking]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (hasStartedRef.current) {
        dispatch(paymentActions.stopCheckingStatus());
      }
    };
  }, [dispatch]);

  // Handle success callback
  useEffect(() => {
    try {
      if (transactionStatus && onSuccess) {
        const { status } = transactionStatus;

        if (status === 2 || status === 3) {
          onSuccess(transactionStatus);
        }
      }
    } catch (error) {
      if (onError) {
        onError(`Error in success callback: ${error}`);
      }
    }
  }, [transactionStatus, onSuccess, onError]);

  // Handle error callback
  useEffect(() => {
    try {
      if (statusError && onError) {
        onError(statusError);
      }
    } catch (error) {
      // Silent error handling
    }
  }, [statusError, onError]);

  return {
    // State
    transactionStatus,
    isCheckingStatus,
    checkCount,
    statusLoading,
    statusError,
    
    // Actions
    startChecking,
    stopChecking,
    resetStatus,
    
    // Computed
    isSuccess: transactionStatus?.status === 2 || transactionStatus?.status === 3,
    isPending: transactionStatus?.status === 1,
    isFailed: transactionStatus?.status && ![1, 2, 3].includes(transactionStatus.status)
  };
};
