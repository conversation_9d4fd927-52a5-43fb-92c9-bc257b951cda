import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  paymentActions, 
  selectTransactionStatus, 
  selectIsCheckingStatus,
  selectCheckCount,
  selectStatusLoading,
  selectStatusError
} from '../store/slices/paymentSlice';

interface UseTransactionStatusProps {
  transactionId?: string;
  params?: any;
  enabled?: boolean;
  onSuccess?: (status: any) => void;
  onError?: (error: string) => void;
}

export const useTransactionStatus = ({
  transactionId,
  params,
  enabled = true,
  onSuccess,
  onError
}: UseTransactionStatusProps) => {
  const dispatch = useDispatch();
  
  // Selectors
  const transactionStatus = useSelector(selectTransactionStatus);
  const isCheckingStatus = useSelector(selectIsCheckingStatus);
  const checkCount = useSelector(selectCheckCount);
  const statusLoading = useSelector(selectStatusLoading);
  const statusError = useSelector(selectStatusError);

  // Start checking when enabled and have required params
  useEffect(() => {
    if (enabled && transactionId && params) {
      dispatch(paymentActions.checkTransactionStatus({
        transactionId,
        params
      }));
    }

    // Cleanup when disabled
    return () => {
      if (!enabled) {
        dispatch(paymentActions.stopCheckingStatus());
      }
    };
  }, [enabled, transactionId, params, dispatch]);

  // Handle success callback
  useEffect(() => {
    if (transactionStatus && onSuccess) {
      const { status } = transactionStatus;
      if (status === 2 || status === 3) {
        onSuccess(transactionStatus);
      }
    }
  }, [transactionStatus, onSuccess]);

  // Handle error callback
  useEffect(() => {
    if (statusError && onError) {
      onError(statusError);
    }
  }, [statusError, onError]);

  // Control functions
  const startChecking = () => {
    if (transactionId && params) {
      dispatch(paymentActions.checkTransactionStatus({
        transactionId,
        params
      }));
    }
  };

  const stopChecking = () => {
    dispatch(paymentActions.stopCheckingStatus());
  };

  const resetStatus = () => {
    dispatch(paymentActions.resetCheckCount());
    dispatch(paymentActions.stopCheckingStatus());
  };

  return {
    // State
    transactionStatus,
    isCheckingStatus,
    checkCount,
    statusLoading,
    statusError,
    
    // Actions
    startChecking,
    stopChecking,
    resetStatus,
    
    // Computed
    isSuccess: transactionStatus?.status === 2 || transactionStatus?.status === 3,
    isPending: transactionStatus?.status === 1,
    isFailed: transactionStatus?.status && ![1, 2, 3].includes(transactionStatus.status)
  };
};
