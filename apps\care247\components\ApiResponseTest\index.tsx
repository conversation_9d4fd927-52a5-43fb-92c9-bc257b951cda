import React, { useState } from 'react';
import { Card, Button, Typography, Alert } from 'antd';

const { Title, Text } = Typography;

const ApiResponseTest: React.FC = () => {
  const [result, setResult] = useState<any>(null);

  // Mock actual API response structure
  const mockApiResponse = {
    "status": 200,
    "message": null,
    "gatewayMessage": null,
    "data": {
      "TransactionInfo": {
        "transactionId": "TT2506207QIQFIHZZITU",
        "orderId": "TT2506207QIQFIHZZITU",
        "userId": "6281c28cf054720019f0e9c1",
        "name": "TESST",
        "partnerId": "binhthanhhcm",
        "tennantId": "6352133cbdaa4ef0eea27778",
        "gatewayId": "appotapay_ebill",
        "gatewayTransactionId": null,
        "amount": 12100,
        "description": "PKH - <PERSON><PERSON> toan <PERSON> kham benh TT2506207QIQFIHZZITU",
        "status": 1,
        "message": "Tạo đơn hàng thành công",
        "createdAt": "2025-06-20T07:52:32.268+00:00",
        "updatedAt": "2025-06-20T07:52:33.091+00:00",
        "gatewayMessage": "Thành công",
        "feeCode": null,
        "billId": null,
        "patientId": null,
        "patientEmrno": null,
        "typeId": null,
        "paymentType": null,
        "phone": null,
        "callbackMedproStatus": null
      }
    },
    "error": null,
    "params": null
  };

  const testApiResponseProcessing = () => {
    const data = mockApiResponse;
    
    // Simulate the processing logic from PaymentGatewayService
    if (data?.data?.TransactionInfo) {
      const { TransactionInfo } = data.data;
      const processedResult = {
        status: TransactionInfo.status,
        description: TransactionInfo.description || TransactionInfo.message || 'Nội dung chuyển khoản',
        partnerId: TransactionInfo.partnerId,
        originalData: data
      };
      
      setResult(processedResult);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>API Response Test</Title>
      <Text type="secondary">
        Test để verify rằng actual API response được xử lý đúng
      </Text>

      <Card title="Test Controls" style={{ marginTop: '24px' }}>
        <Button 
          type="primary" 
          onClick={testApiResponseProcessing}
        >
          Test API Response Processing
        </Button>
      </Card>

      {result && (
        <Card title="Processed Result" style={{ marginTop: '16px' }}>
          <Alert
            message="Processing Result"
            description={
              <div>
                <div><strong>Status:</strong> {result.status}</div>
                <div><strong>Description:</strong> {result.description}</div>
                <div><strong>Partner ID:</strong> {result.partnerId}</div>
                <div style={{ marginTop: '16px' }}>
                  <Text strong>Full Result:</Text>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '12px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto',
                    marginTop: '8px'
                  }}>
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </div>
              </div>
            }
            type="success"
            showIcon
          />
        </Card>
      )}

      <Card title="Expected Results" style={{ marginTop: '16px' }}>
        <div>
          <Text><strong>Status:</strong> 1 (from TransactionInfo.status)</Text><br />
          <Text><strong>Description:</strong> "PKH - Thanh toan Phieu kham benh TT2506207QIQFIHZZITU" (from TransactionInfo.description)</Text><br />
          <Text><strong>Partner ID:</strong> "binhthanhhcm" (from TransactionInfo.partnerId)</Text><br />
          <Text type="secondary">Không phải "Nội dung chuyển khoản" nữa!</Text>
        </div>
      </Card>

      <Card title="Original API Response" style={{ marginTop: '16px' }}>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '12px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto'
        }}>
          {JSON.stringify(mockApiResponse, null, 2)}
        </pre>
      </Card>
    </div>
  );
};

export default ApiResponseTest;
