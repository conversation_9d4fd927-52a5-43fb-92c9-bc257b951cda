import React, { useState } from 'react';
import { Card, Button, Input, Space, Typography, Alert, message } from 'antd';
import PaymentGatewayService, { TransactionInfo, ApiResponse } from '../../services/paymentGatewayService';

const { Title, Text } = Typography;

const TransactionInfoTest: React.FC = () => {
  const [transactionId, setTransactionId] = useState('TT250620WHLPYGSPHUBC');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Mock actual API response structure
  const mockApiResponse: ApiResponse = {
    TransactionInfo: {
      transactionId: "TT250620WHLPYGSPHUBC",
      orderId: "TT250620WHLPYGSPHUBC",
      userId: null,
      name: null,
      partnerId: "binhthanhhcm",
      tennantId: null,
      gatewayId: "appotapay_ebill",
      gatewayTransactionId: "",
      amount: null,
      description: null,
      status: 1,
      message: "Thanh toán không thành công",
      createdAt: null,
      updatedAt: null,
      gatewayMessage: "Thành công",
      feeCode: null,
      billId: null,
      patientId: null,
      patientEmrno: null,
      typeId: null,
      paymentType: null,
      phone: null,
      callbackMedproStatus: null
    }
  };

  const testActualApiStructure = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      // Simulate actual API response processing
      console.log('Testing actual API response structure:', mockApiResponse);
      
      // Process the response like our service would
      const { TransactionInfo } = mockApiResponse;
      const normalizedResponse = {
        status: TransactionInfo.status,
        description: TransactionInfo.message || TransactionInfo.description || 'Unknown status',
        partnerId: TransactionInfo.partnerId,
        data: mockApiResponse
      };

      console.log('Normalized response:', normalizedResponse);
      setResponse(normalizedResponse);
      message.success('API response processed successfully!');
      
    } catch (err: any) {
      console.error('Error processing API response:', err);
      setError(err.message || 'Error processing response');
      message.error('Failed to process API response');
    } finally {
      setLoading(false);
    }
  };

  const testServiceMethod = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      console.log('Testing service method with transactionId:', transactionId);
      const result = await PaymentGatewayService.getTransactionStatusMock(transactionId);
      console.log('Service method result:', result);
      setResponse(result);
      message.success('Service method test successful!');
      
    } catch (err: any) {
      console.error('Service method error:', err);
      setError(err.message || 'Service method failed');
      message.error('Service method test failed');
    } finally {
      setLoading(false);
    }
  };

  const testBinhthanhhcmPartner = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const testId = 'TT_binhthanhhcm_TEST';
      console.log('Testing binhthanhhcm partner with ID:', testId);
      const result = await PaymentGatewayService.getTransactionStatusMock(testId);
      console.log('Binhthanhhcm test result:', result);
      setResponse(result);
      message.success('Binhthanhhcm partner test successful!');
      
    } catch (err: any) {
      console.error('Binhthanhhcm test error:', err);
      setError(err.message || 'Binhthanhhcm test failed');
      message.error('Binhthanhhcm test failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>TransactionInfo API Test</Title>
      <Text type="secondary">
        Test component để verify API response structure với TransactionInfo
      </Text>

      <Card title="Test Controls" style={{ marginTop: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Transaction ID:</Text>
            <Input
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Nhập transaction ID"
              style={{ marginTop: '8px' }}
              disabled={loading}
            />
          </div>
          
          <Space wrap>
            <Button 
              type="primary" 
              onClick={testActualApiStructure}
              loading={loading}
            >
              Test Actual API Structure
            </Button>
            <Button 
              onClick={testServiceMethod}
              loading={loading}
            >
              Test Service Method
            </Button>
            <Button 
              onClick={testBinhthanhhcmPartner}
              loading={loading}
            >
              Test binhthanhhcm Partner
            </Button>
          </Space>
        </Space>
      </Card>

      {error && (
        <Alert
          message="Test Error"
          description={error}
          type="error"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}

      {response && (
        <Card title="Test Response" style={{ marginTop: '16px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>Status: </Text>
              <Text style={{ 
                color: response.status === 1 ? '#faad14' : 
                       response.status === 2 || response.status === 3 ? '#52c41a' : '#ff4d4f'
              }}>
                {response.status} ({
                  response.status === 1 ? 'Pending' :
                  response.status === 2 || response.status === 3 ? 'Success' : 'Failed'
                })
              </Text>
            </div>
            
            <div>
              <Text strong>Description: </Text>
              <Text>{response.description || 'N/A'}</Text>
            </div>
            
            <div>
              <Text strong>Partner ID: </Text>
              <Text code>{response.partnerId || 'N/A'}</Text>
            </div>
            
            <div>
              <Text strong>Full Response:</Text>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                marginTop: '8px'
              }}>
                {JSON.stringify(response, null, 2)}
              </pre>
            </div>
          </Space>
        </Card>
      )}

      <Card title="Expected API Response Structure" style={{ marginTop: '16px' }}>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '12px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto'
        }}>
          {JSON.stringify(mockApiResponse, null, 2)}
        </pre>
      </Card>

      <Card title="Status Codes" style={{ marginTop: '16px' }}>
        <Space direction="vertical">
          <Text><strong>1:</strong> Pending - Giao dịch đang chờ xử lý</Text>
          <Text><strong>2:</strong> Success - Giao dịch thành công</Text>
          <Text><strong>3:</strong> Success - Giao dịch thành công (alternative)</Text>
          <Text><strong>4+:</strong> Failed - Giao dịch thất bại</Text>
        </Space>
      </Card>
    </div>
  );
};

export default TransactionInfoTest;
