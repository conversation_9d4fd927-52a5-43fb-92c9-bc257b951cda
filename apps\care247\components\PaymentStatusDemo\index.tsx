import React, { useState } from 'react';
import { Card, Button, Input, Space, Typography, Divider } from 'antd';
import { useTransactionStatus } from '../../hooks/useTransactionStatus';
import PaymentStatusExample from '../PaymentCard/PaymentStatusExample';

const { Title, Text } = Typography;

const PaymentStatusDemo: React.FC = () => {
  const [transactionId, setTransactionId] = useState('TXN123456789');
  const [isEnabled, setIsEnabled] = useState(false);

  // Mock payment params
  const mockParams = {
    transactionId,
    expiredTime: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes from now
    redirectUrl: '/',
    qrCodeUrl: 'https://example.com/qr.png',
    bankCode: 'VCB',
    bankName: 'Vietcombank',
    accountNo: '**********',
    accountName: 'NGUYEN VAN A',
    description: 'Thanh toan phi kham benh'
  };

  const {
    transactionStatus,
    isCheckingStatus,
    statusLoading,
    statusError,
    isSuccess,
    isPending,
    isFailed,
    checkCount,
    startChecking,
    stopChecking,
    resetStatus
  } = useTransactionStatus({
    transactionId,
    params: mockParams,
    enabled: isEnabled,
    onSuccess: (status) => {
      console.log('Demo: Payment successful!', status);
    },
    onError: (error) => {
      console.error('Demo: Payment error!', error);
    }
  });

  const handleStart = () => {
    setIsEnabled(true);
    startChecking();
  };

  const handleStop = () => {
    setIsEnabled(false);
    stopChecking();
  };

  const handleReset = () => {
    setIsEnabled(false);
    resetStatus();
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Payment Status Demo</Title>
      <Text type="secondary">
        Demo component để test saga pattern cho check transaction status
      </Text>

      <Divider />

      <Card title="Controls" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Transaction ID:</Text>
            <Input
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Nhập transaction ID"
              style={{ marginTop: '8px' }}
            />
          </div>
          
          <Space>
            <Button 
              type="primary" 
              onClick={handleStart}
              disabled={isCheckingStatus}
            >
              Bắt đầu kiểm tra
            </Button>
            <Button 
              onClick={handleStop}
              disabled={!isCheckingStatus}
            >
              Dừng kiểm tra
            </Button>
            <Button onClick={handleReset}>
              Reset
            </Button>
          </Space>
        </Space>
      </Card>

      <Card title="Status Information" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Enabled: </Text>
            <Text>{isEnabled ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Is Checking: </Text>
            <Text>{isCheckingStatus ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Status Loading: </Text>
            <Text>{statusLoading ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Check Count: </Text>
            <Text>{checkCount}</Text>
          </div>
          <div>
            <Text strong>Is Success: </Text>
            <Text>{isSuccess ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Is Pending: </Text>
            <Text>{isPending ? 'Yes' : 'No'}</Text>
          </div>
          <div>
            <Text strong>Is Failed: </Text>
            <Text>{isFailed ? 'Yes' : 'No'}</Text>
          </div>
          {statusError && (
            <div>
              <Text strong>Error: </Text>
              <Text type="danger">{statusError}</Text>
            </div>
          )}
          {transactionStatus && (
            <div>
              <Text strong>Transaction Status: </Text>
              <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                {JSON.stringify(transactionStatus, null, 2)}
              </pre>
            </div>
          )}
        </Space>
      </Card>

      <Card title="Payment Status Component">
        <PaymentStatusExample
          transactionId={transactionId}
          params={mockParams}
          onSuccess={() => console.log('PaymentStatusExample: Success!')}
          onError={(error) => console.error('PaymentStatusExample: Error!', error)}
        />
      </Card>
    </div>
  );
};

export default PaymentStatusDemo;
