import moment from "moment";
import "moment/locale/vi";
import React, { useEffect, useState } from "react";
import { timeHHMMSS } from "../../utils/constants";
import styles from "./styles.module.less";

let timer: NodeJS.Timeout;

interface CountdownProps {
  payment: {
    expiredTime?: string;
    redirectUrl?: string;
  };
  onExpired?: () => void;
}

interface TimeState {
  hours: string;
  minutes: string;
  seconds: string;
}

interface CountdownState {
  countdown: TimeState;
  countdownStatus: boolean;
}

const Countdown: React.FC<CountdownProps> = ({ payment, onExpired }) => {
  const timeInit: TimeState = {
    hours: "00",
    minutes: "00",
    seconds: "00"
  };

  const stateInit: CountdownState = {
    countdown: timeInit,
    countdownStatus: false
  };

  const [state, setState] = useState<CountdownState>(stateInit);

  useEffect(() => {
    if (payment?.expiredTime) {
      timer = setInterval(function() {
        const now = moment().utc();
        const then = moment(payment?.expiredTime).utc();
        const { time, resolveTime } = timeHHMMSS({ now, then });

        if (time < 1) {
          clearInterval(timer);
          if (onExpired) {
            onExpired();
          }
        }

        setState(prev => ({
          ...prev,
          countdown: time < 1 ? timeInit : resolveTime,
          countdownStatus: time < 1
        }));
        return resolveTime;
      }, 1000);
    }

    return () => {
      clearInterval(timer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [payment, onExpired]);

  const handleCompletedPayment = () => {
    const { redirectUrl } = payment;
    if (!redirectUrl) return;
    
    const isDeeplink = !redirectUrl.startsWith("http");

    window.location.href = isDeeplink
      ? redirectUrl + "&vnp=VNPAY"
      : redirectUrl;
  };

  const { countdown, countdownStatus } = state;

  if (countdownStatus) {
    handleCompletedPayment();
  }

  return (
    <React.Fragment>
      <div className={styles.countDown}>
        <div className={styles.time}>
          {countdown?.hours !== "00" && (
            <>
              <p>{countdown?.hours}</p>
              <span>:</span>
            </>
          )}
          <p>{countdown?.minutes}</p>
          <span>:</span>
          <p>{countdown?.seconds}</p>
        </div>
      </div>
    </React.Fragment>
  );
};

export default Countdown;
